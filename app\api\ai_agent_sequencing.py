from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict
from datetime import datetime

from ..models.order_item import OrderItem, ItemStatus
from ..models.kitchen_station import KitchenStation
from ..core.database import get_db
from ..services.ollama_agent import OllamaSequencingAgent
from ..core.config import settings

router = APIRouter()

# Initialize AI Agent
ai_agent = OllamaSequencingAgent(
    ollama_host=settings.OLLAMA_HOST,
    model=settings.OLLAMA_MODEL
)

@router.post("/items/ai-sequenced")
async def create_ai_sequenced_item(
    item_data: dict,
    db: Session = Depends(get_db)
):
    """Create order item using AI agent for sequencing"""
    
    try:
        # Validate kitchen station
        kitchen = db.query(KitchenStation).filter(
            KitchenStation.id == item_data["kitchen_station_id"]
        ).first()
        
        if not kitchen:
            raise HTTPException(status_code=404, detail="Kitchen station not found")
        
        # Get existing orders
        existing_orders = db.query(OrderItem).filter(
            OrderItem.order_id == item_data["order_id"]
        ).all()
        
        # Get current kitchen queue
        kitchen_queue = db.query(OrderItem).filter(
            OrderItem.kitchen_station_id == item_data["kitchen_station_id"],
            OrderItem.status.in_([ItemStatus.NEW, ItemStatus.IN_PREPARATION])
        ).all()
        
        # Prepare kitchen data
        kitchen_data = {
            'name': kitchen.name,
            'max_capacity': kitchen.max_capacity,
            'current_load': len([item for item in kitchen_queue if item.status == ItemStatus.IN_PREPARATION]),
            'average_prep_time': kitchen.average_prep_time
        }
        
        # Get AI sequencing decision
        ai_result = await ai_agent.get_sequencing_decision(
            item_data, existing_orders, kitchen_queue, kitchen_data
        )
        
        if not ai_result['success']:
            raise HTTPException(status_code=500, detail="AI sequencing failed")
        
        decision = ai_result['decision']
        
        # Create order item with AI decision
        new_item = OrderItem(
            order_id=item_data["order_id"],
            item_name=item_data["item_name"],
            kitchen_station_id=item_data["kitchen_station_id"],
            estimated_prep_time=item_data["estimated_prep_time"],
            priority=item_data.get("priority", "medium"),
            sequence_number=decision['sequence_position'],
            ai_reasoning=f"AI Agent ({ai_result['model_used']}): {decision['reasoning']}"
        )
        
        db.add(new_item)
        db.commit()
        db.refresh(new_item)
        
        return {
            "message": "Item sequenced by AI agent",
            "item_id": new_item.id,
            "sequence_number": new_item.sequence_number,
            "ai_model": ai_result['model_used'],
            "reasoning": decision['reasoning'],
            "coordination_strategy": decision.get('coordination_strategy', 'none'),
            "timing_delay": decision.get('timing_delay', 0),
            "order_completion_time": decision.get('order_completion_time', item_data['estimated_prep_time'])
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create item: {str(e)}")

@router.post("/resequence/ai/{kitchen_id}")
async def ai_resequence_kitchen(
    kitchen_id: str,
    db: Session = Depends(get_db)
):
    """Resequence kitchen queue using AI agent"""
    
    try:
        # Get kitchen data
        kitchen = db.query(KitchenStation).filter(KitchenStation.id == kitchen_id).first()
        if not kitchen:
            raise HTTPException(status_code=404, detail="Kitchen station not found")
        
        # Get all pending items
        items = db.query(OrderItem).filter(
            OrderItem.kitchen_station_id == kitchen_id,
            OrderItem.status.in_([ItemStatus.NEW, ItemStatus.IN_PREPARATION])
        ).all()
        
        if not items:
            return {"message": "No items to resequence", "items_resequenced": 0}
        
        # Prepare kitchen data
        kitchen_data = {
            'name': kitchen.name,
            'max_capacity': kitchen.max_capacity,
            'current_load': len([item for item in items if item.status == ItemStatus.IN_PREPARATION])
        }
        
        # Get AI resequencing decision
        ai_result = await ai_agent.resequence_kitchen_with_ai(items, kitchen_data)
        
        if not ai_result['success']:
            raise HTTPException(status_code=500, detail="AI resequencing failed")
        
        # Apply AI sequence decisions
        sequence_map = {item['item_id']: item for item in ai_result['sequence']}
        
        for item in items:
            if item.id in sequence_map:
                ai_decision = sequence_map[item.id]
                item.sequence_number = ai_decision['position']
                item.ai_reasoning = f"AI Agent ({ai_result['model_used']}): {ai_decision['reasoning']}"
        
        db.commit()
        
        return {
            "message": f"AI resequenced {len(items)} items",
            "items_resequenced": len(items),
            "ai_model": ai_result['model_used'],
            "sequence": [
                {
                    "item_id": item.id,
                    "sequence": item.sequence_number,
                    "item_name": item.item_name,
                    "order_id": item.order_id,
                    "reasoning": item.ai_reasoning,
                    "start_delay": sequence_map.get(item.id, {}).get('start_delay', 0)
                }
                for item in sorted(items, key=lambda x: x.sequence_number)
            ]
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to resequence: {str(e)}")

@router.get("/ai-agent/status")
async def get_ai_agent_status():
    """Check AI agent status and model availability"""
    
    try:
        # Test connection to Ollama
        import requests
        response = requests.get(f"{settings.OLLAMA_HOST}/api/tags", timeout=5)
        
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_available = any(model['name'].startswith(settings.OLLAMA_MODEL) for model in models)
            
            return {
                "status": "online",
                "ollama_host": settings.OLLAMA_HOST,
                "model": settings.OLLAMA_MODEL,
                "model_available": model_available,
                "available_models": [model['name'] for model in models]
            }
        else:
            return {
                "status": "offline",
                "ollama_host": settings.OLLAMA_HOST,
                "model": settings.OLLAMA_MODEL,
                "error": "Ollama service not responding"
            }
            
    except Exception as e:
        return {
            "status": "error",
            "ollama_host": settings.OLLAMA_HOST,
            "model": settings.OLLAMA_MODEL,
            "error": str(e)
        }

@router.post("/ai-agent/test")
async def test_ai_agent():
    """Test AI agent with sample data"""
    
    sample_item = {
        "order_id": "TEST-AI-001",
        "item_name": "Test Burger",
        "kitchen_station_id": "grill_001",
        "estimated_prep_time": 15,
        "priority": "medium"
    }
    
    sample_kitchen_data = {
        "name": "Test Kitchen",
        "max_capacity": 5,
        "current_load": 2,
        "average_prep_time": 12.0
    }
    
    try:
        result = await ai_agent.get_sequencing_decision(
            sample_item, [], [], sample_kitchen_data
        )
        
        return {
            "test_status": "success" if result['success'] else "failed",
            "ai_model": result['model_used'],
            "decision": result['decision'],
            "sample_reasoning": result['decision'].get('reasoning', 'No reasoning provided')
        }
        
    except Exception as e:
        return {
            "test_status": "error",
            "error": str(e)
        }