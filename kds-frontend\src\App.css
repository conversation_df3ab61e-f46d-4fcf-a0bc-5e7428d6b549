.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Order Overview Styles */
.order-overview {
  padding: 1rem;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  color: white;
}

.switch-mode-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s;
}

.switch-mode-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.new-order-section {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.item-selector {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
}

.item-selector select {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
}

.order-id-section {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.order-id-section input {
  flex: 1;
}

.generate-order-btn {
  padding: 0.75rem 1rem;
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  white-space: nowrap;
}

.generate-order-btn:hover {
  background: #45a049;
}

.item-selector button {
  padding: 0.75rem 1.5rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.order-items {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.order-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  margin: 0.5rem 0;
  background: white;
  border-radius: 6px;
}

.submit-order-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
}

.orders-header {
  margin-bottom: 20px;
}

.tab-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tab-btn {
  padding: 10px 20px;
  border: 2px solid #ddd;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.tab-btn:hover:not(.active) {
  background: #f5f5f5;
  border-color: #bbb;
}

.orders-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.order-card {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.order-card.completed {
  border-left: 4px solid #4caf50;
  background: #f9fff9;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.order-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.order-stats {
  display: flex;
  gap: 1rem;
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.stations-list {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.station-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-radius: 8px;
  border-left: 4px solid #e0e0e0;
}

.item-row.in_preparation {
  border-left-color: #ffc107;
  background: #fff8e1;
}

.item-row.ready {
  border-left-color: #28a745;
  background: #f1f8e9;
}

.item-row.served {
  border-left-color: #6c757d;
  background: #f8f9fa;
  opacity: 0.7;
}

.complete-order-btn {
  width: 100%;
  padding: 0.75rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

.completion-badge {
  background: #4caf50;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: bold;
}

.completion-metrics {
  display: flex;
  gap: 20px;
  margin: 15px 0;
  padding: 15px;
  background: #f0f8f0;
  border-radius: 8px;
}

.completion-metrics .metric {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.completion-metrics .metric-label {
  font-size: 0.8em;
  color: #666;
  margin-bottom: 4px;
}

.completion-metrics .metric-value {
  font-weight: bold;
  font-size: 1.1em;
}

.completion-metrics .metric-value.good {
  color: #4caf50;
}

.completion-metrics .metric-value.needs-improvement {
  color: #ff9800;
}

.order-summary {
  margin-top: 15px;
}

.summary-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
}

.summary-stats span {
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 0.9em;
}

.over-time {
  color: #f44336 !important;
  background: #ffebee !important;
}

.under-time {
  color: #4caf50 !important;
  background: #e8f5e8 !important;
}

.completion-time {
  font-size: 0.9em;
  color: #666;
  text-align: right;
}

/* Kitchen Manager Styles */
.kitchen-manager {
  padding: 1rem;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  color: white;
}

.kitchen-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  height: calc(100vh - 200px);
}

.kitchen-sidebar {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.stations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.station-card {
  padding: 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.station-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.station-card.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.station-card:hover {
  background: #f5f5f5;
  cursor: pointer;
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.load-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  color: white;
  font-weight: 600;
  font-size: 0.8rem;
}

.station-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.next-item {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #667eea;
  font-weight: 500;
}

.kitchen-details {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.kitchen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.kitchen-metrics {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-label {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.empty-queue {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.queue-item-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  margin: 1rem 0;
  border-radius: 12px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s;
}

.queue-item-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.queue-item-card.in_preparation {
  border-color: #ffc107;
  background: #fff8e1;
}

.queue-item-card.ready {
  border-color: #28a745;
  background: #f1f8e9;
}

.item-sequence {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.sequence-number {
  background: #667eea;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.next-badge {
  background: #ff4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.item-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-main h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.item-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.item-status-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.8rem;
}

.status-badge.new {
  background: #e3f2fd;
  color: #1976d2;
}

.status-badge.in_preparation {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.ready {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.no-kitchen-selected {
  text-align: center;
  padding: 3rem;
  color: #666;
}

@media (max-width: 768px) {
  .kitchen-layout {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .orders-container {
    grid-template-columns: 1fr;
  }
  
  .item-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .queue-item-card {
    flex-direction: column;
  }
}



.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.order-form {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.order-form h2 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.form-grid input, .form-grid select {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-grid input:focus, .form-grid select:focus {
  outline: none;
  border-color: #667eea;
}

.create-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.create-btn:hover {
  transform: translateY(-2px);
}

.queues-container h2 {
  color: white;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.queues-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.queue-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.queue-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.queue-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.queue-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
}

.resequence-btn {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.3s;
}

.resequence-btn:hover {
  background: rgba(255,255,255,0.3);
}

.queue-items {
  max-height: 600px;
  overflow-y: auto;
}

.queue-item {
  padding: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.3s;
}

.queue-item:hover {
  background: #f8f9fa;
}

.queue-item.in_preparation {
  border-left: 4px solid #ffc107;
  background: #fff8e1;
}

.queue-item.ready {
  border-left: 4px solid #28a745;
  background: #f1f8e9;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.sequence {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.item-name {
  font-weight: 600;
  color: #333;
  flex: 1;
  min-width: 150px;
}

.priority {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.priority.low { background: #e3f2fd; color: #1976d2; }
.priority.medium { background: #fff3e0; color: #f57c00; }
.priority.high { background: #fce4ec; color: #c2185b; }
.priority.urgent { background: #ffebee; color: #d32f2f; }

.item-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #666;
  flex-wrap: wrap;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status.new { background: #e8f5e8; color: #2e7d32; }
.status.in_preparation { background: #fff8e1; color: #f57c00; }
.status.ready { background: #e8f5e8; color: #2e7d32; }

.ai-reasoning {
  background: #f0f4ff;
  border: 1px solid #d1e7ff;
  border-radius: 8px;
  padding: 0.75rem;
  margin: 1rem 0;
  font-size: 0.85rem;
  color: #1565c0;
  line-height: 1.4;
}

.item-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.status-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: transform 0.2s;
}

.status-btn:hover {
  transform: translateY(-1px);
}

.status-btn.start {
  background: #2196f3;
  color: white;
}

.status-btn.ready {
  background: #4caf50;
  color: white;
}

.status-btn.served {
  background: #ff9800;
  color: white;
}

@media (max-width: 768px) {
  .queues-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .queue-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .item-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
}