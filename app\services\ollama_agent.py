import requests
import json
from typing import Dict, List, Any
from datetime import datetime

class OllamaSequencingAgent:
    """AI Agent using Ollama model for intelligent order sequencing"""
    
    def __init__(self, ollama_host: str = "http://************:11434", model: str = "llama3.2"):
        self.ollama_host = ollama_host
        self.model = model
        self.base_url = f"{ollama_host}/api/generate"
    
    def create_sequencing_prompt(self, item_data: Dict, existing_orders: List, kitchen_queue: List, kitchen_data: Dict) -> str:
        """Create comprehensive prompt for AI sequencing"""
        
        prompt = f"""You are an expert Kitchen Operations AI responsible for intelligent order sequencing in a restaurant kitchen.

CURRENT SITUATION:
- Kitchen: {kitchen_data.get('name', 'Unknown')} (Capacity: {kitchen_data.get('current_load', 0)}/{kitchen_data.get('max_capacity', 5)})
- New Item: {item_data['item_name']} (Order: {item_data['order_id']}, Prep: {item_data['estimated_prep_time']}min, Priority: {item_data.get('priority', 'medium')})

EXISTING ORDERS IN SYSTEM:
"""
        
        # Add existing order information
        orders_map = {}
        for order in existing_orders:
            if order.order_id not in orders_map:
                orders_map[order.order_id] = []
            orders_map[order.order_id].append(order)
        
        for order_id, items in orders_map.items():
            prompt += f"\nOrder {order_id}:\n"
            for item in items:
                prompt += f"  - {item.item_name} ({item.estimated_prep_time}min, {item.kitchen_station_id})\n"
        
        prompt += f"\nCURRENT KITCHEN QUEUE ({len(kitchen_queue)} items):\n"
        for i, item in enumerate(kitchen_queue, 1):
            prompt += f"{i}. {item.item_name} (Order: {item.order_id}, {item.estimated_prep_time}min, {item.priority})\n"
        
        prompt += f"""
SEQUENCING RULES:
1. Multi-item orders must be coordinated - all items finish at same time
2. Critical path item (longest prep time) starts first in multi-item orders
3. Other items in same order start later to synchronize completion
4. Priority levels: urgent > high > medium > low
5. Consider kitchen capacity and current load
6. Provide clear reasoning for sequencing decision

TASK: Determine the optimal sequence position (1-{len(kitchen_queue) + 1}) for the new item and provide detailed reasoning.

Consider:
- Is this part of a multi-item order that needs coordination?
- What is the critical path for order completion?
- How does priority affect positioning?
- What timing delays are needed for coordination?

Respond in JSON format:
{{
    "sequence_position": <number>,
    "reasoning": "<detailed explanation>",
    "coordination_strategy": "<strategy if multi-item>",
    "timing_delay": <minutes to delay if needed>,
    "order_completion_time": <total minutes for order>
}}
"""
        return prompt
    
    async def get_sequencing_decision(self, item_data: Dict, existing_orders: List, kitchen_queue: List, kitchen_data: Dict) -> Dict:
        """Get AI sequencing decision from Ollama model"""
        
        try:
            # Create prompt
            prompt = self.create_sequencing_prompt(item_data, existing_orders, kitchen_queue, kitchen_data)
            
            # Call Ollama API
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "format": "json"
            }
            
            response = requests.post(self.base_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '{}')
                
                try:
                    # Parse AI response
                    decision = json.loads(ai_response)
                    
                    # Validate response format
                    required_fields = ['sequence_position', 'reasoning']
                    if all(field in decision for field in required_fields):
                        return {
                            'success': True,
                            'decision': decision,
                            'model_used': self.model
                        }
                    else:
                        return self._fallback_decision(item_data, existing_orders, kitchen_queue)
                        
                except json.JSONDecodeError:
                    return self._fallback_decision(item_data, existing_orders, kitchen_queue)
            else:
                return self._fallback_decision(item_data, existing_orders, kitchen_queue)
                
        except Exception as e:
            print(f"Ollama API error: {e}")
            return self._fallback_decision(item_data, existing_orders, kitchen_queue)
    
    def _fallback_decision(self, item_data: Dict, existing_orders: List, kitchen_queue: List) -> Dict:
        """Fallback logic when AI model is unavailable"""
        
        # Simple priority-based positioning
        priority_values = {'urgent': 4, 'high': 3, 'medium': 2, 'low': 1}
        item_priority = priority_values.get(item_data.get('priority', 'medium'), 2)
        
        position = 1
        for queued_item in kitchen_queue:
            queued_priority = priority_values.get(queued_item.priority, 2)
            if item_priority <= queued_priority:
                position += 1
        
        # Check for multi-item coordination
        same_order_items = [item for item in existing_orders if item.order_id == item_data['order_id']]
        
        if same_order_items:
            # Multi-item order logic
            all_prep_times = [item.estimated_prep_time for item in same_order_items] + [item_data['estimated_prep_time']]
            max_prep_time = max(all_prep_times)
            is_critical_path = item_data['estimated_prep_time'] == max_prep_time
            
            if is_critical_path:
                reasoning = f"Fallback: Critical path item for order {item_data['order_id']}. Must start first for coordination."
                coordination_strategy = "critical_path_first"
                timing_delay = 0
            else:
                delay = max_prep_time - item_data['estimated_prep_time']
                reasoning = f"Fallback: Supporting item for order {item_data['order_id']}. Delayed {delay}min for coordination."
                coordination_strategy = "delayed_start"
                timing_delay = delay
        else:
            reasoning = f"Fallback: Single item order. Positioned by priority ({item_data.get('priority', 'medium')})."
            coordination_strategy = "none"
            timing_delay = 0
        
        return {
            'success': True,
            'decision': {
                'sequence_position': position,
                'reasoning': reasoning,
                'coordination_strategy': coordination_strategy,
                'timing_delay': timing_delay,
                'order_completion_time': item_data['estimated_prep_time']
            },
            'model_used': 'fallback_logic'
        }
    
    async def resequence_kitchen_with_ai(self, items: List, kitchen_data: Dict) -> Dict:
        """Use AI to resequence entire kitchen queue"""
        
        if not items:
            return {'success': True, 'sequence': [], 'model_used': 'none'}
        
        # Create resequencing prompt
        prompt = f"""You are an expert Kitchen Operations AI. Resequence the following {len(items)} items in the optimal order.

KITCHEN: {kitchen_data.get('name', 'Unknown')} (Capacity: {kitchen_data.get('max_capacity', 5)})

ITEMS TO SEQUENCE:
"""
        
        # Group items by order
        orders_map = {}
        for item in items:
            if item.order_id not in orders_map:
                orders_map[item.order_id] = []
            orders_map[item.order_id].append(item)
        
        for order_id, order_items in orders_map.items():
            prompt += f"\nOrder {order_id} ({len(order_items)} items):\n"
            for item in order_items:
                prompt += f"  - {item.item_name} ({item.estimated_prep_time}min, Priority: {item.priority})\n"
        
        prompt += f"""
SEQUENCING RULES:
1. Multi-item orders must finish simultaneously
2. Critical path items (longest prep) start first
3. Coordinate timing for order completion
4. Respect priority levels: urgent > high > medium > low
5. Optimize kitchen efficiency

Provide the optimal sequence as JSON:
{{
    "sequence": [
        {{
            "item_id": <id>,
            "position": <1-{len(items)}>,
            "reasoning": "<why this position>",
            "start_delay": <minutes to delay start>
        }}
    ]
}}
"""
        
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "format": "json"
            }
            
            response = requests.post(self.base_url, json=payload, timeout=45)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '{}')
                
                try:
                    decision = json.loads(ai_response)
                    if 'sequence' in decision:
                        return {
                            'success': True,
                            'sequence': decision['sequence'],
                            'model_used': self.model
                        }
                except json.JSONDecodeError:
                    pass
            
            # Fallback to simple resequencing
            return self._fallback_resequence(items)
            
        except Exception as e:
            print(f"AI resequencing error: {e}")
            return self._fallback_resequence(items)
    
    def _fallback_resequence(self, items: List) -> Dict:
        """Fallback resequencing logic"""
        
        # Group by order and apply coordination logic
        orders_map = {}
        for item in items:
            if item.order_id not in orders_map:
                orders_map[item.order_id] = []
            orders_map[item.order_id].append(item)
        
        sequence = []
        position = 1
        
        # Handle critical path items first
        for order_id, order_items in orders_map.items():
            if len(order_items) > 1:
                critical_item = max(order_items, key=lambda x: x.estimated_prep_time)
                sequence.append({
                    'item_id': critical_item.id,
                    'position': position,
                    'reasoning': f'Critical path for order {order_id}',
                    'start_delay': 0
                })
                position += 1
        
        # Handle remaining items
        remaining = []
        for order_id, order_items in orders_map.items():
            if len(order_items) == 1:
                remaining.extend(order_items)
            else:
                critical_prep = max(item.estimated_prep_time for item in order_items)
                for item in order_items:
                    if not any(s['item_id'] == item.id for s in sequence):
                        delay = critical_prep - item.estimated_prep_time
                        remaining.append((item, delay))
        
        # Sort remaining by priority
        priority_order = {'urgent': 4, 'high': 3, 'medium': 2, 'low': 1}
        if remaining:
            if isinstance(remaining[0], tuple):
                remaining.sort(key=lambda x: priority_order.get(x[0].priority, 2), reverse=True)
                for item, delay in remaining:
                    sequence.append({
                        'item_id': item.id,
                        'position': position,
                        'reasoning': f'Coordinated item with {delay}min delay',
                        'start_delay': delay
                    })
                    position += 1
            else:
                remaining.sort(key=lambda x: priority_order.get(x.priority, 2), reverse=True)
                for item in remaining:
                    sequence.append({
                        'item_id': item.id,
                        'position': position,
                        'reasoning': f'Single item by priority',
                        'start_delay': 0
                    })
                    position += 1
        
        return {
            'success': True,
            'sequence': sequence,
            'model_used': 'fallback_logic'
        }