{"ast": null, "code": "var _jsxFileName = \"D:\\\\kds_ai_sequencing\\\\kds-frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport OrderOverview from './components/OrderOverview';\nimport KitchenManager from './components/KitchenManager';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentMode, setCurrentMode] = useState('orders'); // 'orders' or 'kitchen'\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: currentMode === 'orders' ? /*#__PURE__*/_jsxDEV(OrderOverview, {\n      onSwitchToKitchen: () => setCurrentMode('kitchen')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(KitchenManager, {\n      onSwitchToOrders: () => setCurrentMode('orders')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"LCd1WQNls5IRAxsYO/Cv136ftN4=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "OrderOverview", "KitchenManager", "jsxDEV", "_jsxDEV", "App", "_s", "currentMode", "setCurrentMode", "className", "children", "onSwitchToKitchen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSwitchToOrders", "_c", "$RefreshReg$"], "sources": ["D:/kds_ai_sequencing/kds-frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport OrderOverview from './components/OrderOverview';\nimport KitchenManager from './components/KitchenManager';\nimport './App.css';\n\nfunction App() {\n  const [currentMode, setCurrentMode] = useState('orders'); // 'orders' or 'kitchen'\n\n  return (\n    <div className=\"App\">\n      {currentMode === 'orders' ? (\n        <OrderOverview onSwitchToKitchen={() => setCurrentMode('kitchen')} />\n      ) : (\n        <KitchenManager onSwitchToOrders={() => setCurrentMode('orders')} />\n      )}\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;;EAE1D,oBACEI,OAAA;IAAKK,SAAS,EAAC,KAAK;IAAAC,QAAA,EACjBH,WAAW,KAAK,QAAQ,gBACvBH,OAAA,CAACH,aAAa;MAACU,iBAAiB,EAAEA,CAAA,KAAMH,cAAc,CAAC,SAAS;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAErEX,OAAA,CAACF,cAAc;MAACc,gBAAgB,EAAEA,CAAA,KAAMR,cAAc,CAAC,QAAQ;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACpE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACT,EAAA,CAZQD,GAAG;AAAAY,EAAA,GAAHZ,GAAG;AAcZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}