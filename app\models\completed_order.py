from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, DateTime, Float, JSON
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class CompletedOrder(Base):
    __tablename__ = "completed_orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(String, nullable=False, unique=True, index=True)
    
    # Timing information
    order_created_at = Column(DateTime, nullable=False)
    order_completed_at = Column(DateTime, nullable=False)
    total_preparation_time = Column(Float, nullable=False)  # in minutes
    total_wait_time = Column(Float, nullable=False)  # in minutes
    
    # Order details
    total_items = Column(Integer, nullable=False)
    kitchen_stations_used = Column(JSON, nullable=False)  # List of station IDs
    
    # Performance metrics
    estimated_total_time = Column(Float, nullable=False)  # in minutes
    actual_vs_estimated = Column(Float, nullable=False)  # difference in minutes
    efficiency_score = Column(Float, nullable=True)  # 0-1 score
    
    # Additional metadata
    order_metadata = Column(JSON, nullable=True)

class CompletedOrderResponse(BaseModel):
    id: int
    order_id: str
    order_created_at: datetime
    order_completed_at: datetime
    total_preparation_time: float
    total_wait_time: float
    total_items: int
    kitchen_stations_used: list
    estimated_total_time: float
    actual_vs_estimated: float
    efficiency_score: Optional[float]
    
    class Config:
        from_attributes = True