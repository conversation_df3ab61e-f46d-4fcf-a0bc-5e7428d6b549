from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./kds_sequencing.db")

engine = create_engine(
    DATABASE_URL,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    from ..models.order_item import Base as OrderItemBase
    from ..models.kitchen_station import Base as KitchenStationBase
    from ..models.menu_item import Base as MenuItemBase
    from ..models.completed_order import Base as CompletedOrderBase
    
    OrderItemBase.metadata.create_all(bind=engine)
    KitchenStationBase.metadata.create_all(bind=engine)
    MenuItemBase.metadata.create_all(bind=engine)
    CompletedOrderBase.metadata.create_all(bind=engine)