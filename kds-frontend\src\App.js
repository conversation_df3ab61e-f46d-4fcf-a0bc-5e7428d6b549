import React, { useState } from 'react';
import OrderOverview from './components/OrderOverview';
import KitchenManager from './components/KitchenManager';
import './App.css';

function App() {
  const [currentMode, setCurrentMode] = useState('orders'); // 'orders' or 'kitchen'

  return (
    <div className="App">
      {currentMode === 'orders' ? (
        <OrderOverview onSwitchToKitchen={() => setCurrentMode('kitchen')} />
      ) : (
        <KitchenManager onSwitchToOrders={() => setCurrentMode('orders')} />
      )}
    </div>
  );
}

export default App;