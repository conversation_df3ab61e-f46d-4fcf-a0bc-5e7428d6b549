from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
import redis.asyncio as redis
from datetime import datetime, timedelta

from ..models.order_item import OrderItemCreate, OrderItemResponse, OrderItemUpdate, ItemStatus
from ..models.kitchen_station import KitchenStationCreate, KitchenStationResponse, KitchenStationUpdate
from ..models.completed_order import CompletedOrder, CompletedOrderResponse
from ..services.queue_manager import QueueManager
from ..core.database import get_db
from ..core.redis_client import get_redis

router = APIRouter()

# Order Item Endpoints
@router.post("/items", response_model=dict)
async def create_order_item(
    item: OrderItemCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Create new order item and add to queue with AI sequencing"""
    
    queue_manager = QueueManager(db, redis_client)
    
    try:
        result = await queue_manager.add_item_to_queue(item.dict())
        
        # Start auto-transitions in background if not already running
        background_tasks.add_task(queue_manager.start_auto_transitions)
        
        return {
            "message": "Item added to queue successfully",
            "item_id": result["id"],
            "sequence_number": result["sequence_number"],
            "ai_reasoning": result["ai_reasoning"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add item: {str(e)}")

@router.put("/items/{item_id}/status")
async def update_item_status(
    item_id: int,
    status_update: dict,
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Update item status (new -> in_preparation -> ready -> served)"""
    
    try:
        new_status = ItemStatus(status_update.get("status"))
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid status")
    
    queue_manager = QueueManager(db, redis_client)
    
    try:
        result = await queue_manager.update_item_status(item_id, new_status)
        
        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])
        
        return {
            "message": "Status updated successfully",
            **result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update status: {str(e)}")

@router.put("/items/{item_id}/status/simple")
async def update_item_status_simple(
    item_id: int,
    status_update: dict,
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Simple status update endpoint for frontend"""
    
    try:
        new_status = ItemStatus(status_update.get("status"))
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid status")
    
    queue_manager = QueueManager(db, redis_client)
    
    try:
        result = await queue_manager.update_item_status(item_id, new_status)
        
        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])
        
        return {"success": True, "status": new_status.value}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update status: {str(e)}")

@router.get("/items/{item_id}", response_model=OrderItemResponse)
async def get_order_item(
    item_id: int,
    db: Session = Depends(get_db)
):
    """Get specific order item details"""
    
    from ..models.order_item import OrderItem
    
    item = db.query(OrderItem).filter(OrderItem.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="Item not found")
    
    return item

@router.get("/kitchen/{kitchen_id}/queue")
async def get_kitchen_queue(
    kitchen_id: str,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Get current queue for specific kitchen station"""
    
    queue_manager = QueueManager(db, redis_client)
    
    status_filter = None
    if status:
        try:
            status_filter = ItemStatus(status)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid status filter")
    
    try:
        queue = await queue_manager.get_kitchen_queue(kitchen_id, status_filter)
        return {
            "kitchen_station_id": kitchen_id,
            "queue_length": len(queue),
            "items": queue
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get queue: {str(e)}")

@router.get("/queue/all")
async def get_all_queues(
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Get queues for all kitchen stations"""
    
    queue_manager = QueueManager(db, redis_client)
    
    try:
        queues = await queue_manager.get_all_queues()
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "total_kitchens": len(queues),
            "queues": queues
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get queues: {str(e)}")

# Kitchen Station Endpoints
@router.post("/kitchen-stations", response_model=KitchenStationResponse)
async def create_kitchen_station(
    station: KitchenStationCreate,
    db: Session = Depends(get_db)
):
    """Create new kitchen station"""
    
    from ..models.kitchen_station import KitchenStation
    
    # Check if station already exists
    existing = db.query(KitchenStation).filter(KitchenStation.id == station.id).first()
    if existing:
        raise HTTPException(status_code=400, detail="Kitchen station already exists")
    
    try:
        new_station = KitchenStation(**station.dict())
        db.add(new_station)
        db.commit()
        db.refresh(new_station)
        
        return new_station
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create station: {str(e)}")

@router.get("/kitchen-stations", response_model=List[KitchenStationResponse])
async def get_kitchen_stations(
    location_id: Optional[str] = None,
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get all kitchen stations"""
    
    from ..models.kitchen_station import KitchenStation
    
    query = db.query(KitchenStation)
    
    if location_id:
        query = query.filter(KitchenStation.location_id == location_id)
    
    if active_only:
        query = query.filter(KitchenStation.is_active == True)
    
    stations = query.all()
    return stations

@router.put("/kitchen-stations/{station_id}")
async def update_kitchen_station(
    station_id: str,
    station_update: KitchenStationUpdate,
    db: Session = Depends(get_db)
):
    """Update kitchen station details"""
    
    from ..models.kitchen_station import KitchenStation
    
    station = db.query(KitchenStation).filter(KitchenStation.id == station_id).first()
    if not station:
        raise HTTPException(status_code=404, detail="Kitchen station not found")
    
    try:
        update_data = station_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(station, field, value)
        
        station.last_updated = datetime.utcnow()
        db.commit()
        
        return {
            "message": "Kitchen station updated successfully",
            "station_id": station_id,
            "updated_fields": list(update_data.keys())
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to update station: {str(e)}")

# Analytics and Insights Endpoints
@router.get("/analytics/performance")
async def get_performance_metrics(
    kitchen_id: Optional[str] = None,
    hours: int = 24,
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Get performance metrics and analytics"""
    
    queue_manager = QueueManager(db, redis_client)
    
    try:
        metrics = await queue_manager.get_performance_metrics(kitchen_id, hours)
        return {
            "period_hours": hours,
            "kitchen_station_id": kitchen_id,
            "metrics": metrics,
            "generated_at": datetime.utcnow().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")

@router.get("/analytics/insights")
async def get_sequencing_insights(
    kitchen_id: Optional[str] = None,
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Get AI sequencing insights and recommendations"""
    
    queue_manager = QueueManager(db, redis_client)
    
    try:
        if kitchen_id:
            items = await queue_manager.get_kitchen_queue(kitchen_id, ItemStatus.NEW)
        else:
            all_queues = await queue_manager.get_all_queues()
            items = []
            for queue in all_queues.values():
                items.extend([item for item in queue if item.get('status') == 'new'])
        
        insights = await queue_manager.ai_engine.get_sequencing_insights(items, all_queues if not kitchen_id else {kitchen_id: kitchen_data.get(kitchen_id, {})})
        
        return {
            "kitchen_station_id": kitchen_id,
            "insights": insights,
            "generated_at": datetime.utcnow().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get insights: {str(e)}")

@router.post("/ai/retrain")
async def retrain_ai_model(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Retrain AI model with recent historical data"""
    
    def train_model():
        from ..models.order_item import OrderItem
        from datetime import timedelta
        
        # Get completed orders from last 30 days
        cutoff = datetime.utcnow() - timedelta(days=30)
        completed_items = db.query(OrderItem).filter(
            OrderItem.created_at >= cutoff,
            OrderItem.status == ItemStatus.SERVED,
            OrderItem.served_time.isnot(None)
        ).all()
        
        if len(completed_items) < 10:
            return {"error": "Insufficient data for training"}
        
        # Prepare training data
        training_data = []
        for item in completed_items:
            if item.preparation_start_time and item.ready_time:
                actual_prep_time = (item.ready_time - item.preparation_start_time).total_seconds() / 60
                
                training_data.append({
                    'prep_time': item.estimated_prep_time,
                    'actual_prep_time': actual_prep_time,
                    'estimated_prep_time': item.estimated_prep_time,
                    'item_name': item.item_name,
                    'kitchen_load': 3,  # Would need to get historical load data
                    'kitchen_capacity': 5,  # Would need to get from kitchen station
                    'efficiency_score': 1.0
                })
        
        # Train the model
        queue_manager = QueueManager(db, redis_client)
        success = queue_manager.ai_engine.train_historical_model(training_data)
        
        return {"success": success, "training_samples": len(training_data)}
    
    background_tasks.add_task(train_model)
    
    return {
        "message": "AI model retraining started in background",
        "status": "initiated"
    }

@router.get("/orders/completed", response_model=List[CompletedOrderResponse])
async def get_completed_orders(
    limit: int = 50,
    hours: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Get completed orders with timing information"""
    
    query = db.query(CompletedOrder).order_by(CompletedOrder.order_completed_at.desc())
    
    if hours:
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        query = query.filter(CompletedOrder.order_completed_at >= cutoff_time)
    
    completed_orders = query.limit(limit).all()
    return completed_orders

@router.get("/orders/{order_id}/status")
async def get_order_status(
    order_id: str,
    db: Session = Depends(get_db)
):
    """Get status of all items in an order"""
    
    from ..models.order_item import OrderItem
    
    items = db.query(OrderItem).filter(OrderItem.order_id == order_id).all()
    if not items:
        raise HTTPException(status_code=404, detail="Order not found")
    
    # Check if order is completed
    completed_order = db.query(CompletedOrder).filter(CompletedOrder.order_id == order_id).first()
    
    return {
        "order_id": order_id,
        "total_items": len(items),
        "completed_items": len([i for i in items if i.status == ItemStatus.SERVED]),
        "is_completed": completed_order is not None,
        "completion_info": {
            "total_time_minutes": completed_order.total_preparation_time if completed_order else None,
            "efficiency_score": completed_order.efficiency_score if completed_order else None
        } if completed_order else None,
        "items": [{
            "id": item.id,
            "item_name": item.item_name,
            "kitchen_station_id": item.kitchen_station_id,
            "status": item.status.value,
            "created_at": item.created_at,
            "preparation_start_time": item.preparation_start_time,
            "ready_time": item.ready_time,
            "served_time": item.served_time
        } for item in items]
    }