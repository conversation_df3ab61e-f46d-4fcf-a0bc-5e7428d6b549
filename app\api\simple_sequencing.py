from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict
from datetime import datetime

from ..models.order_item import OrderItem, ItemStatus
from ..models.kitchen_station import KitchenStation
from ..core.database import get_db

router = APIRouter()

def analyze_order_coordination(item_data: Dict, existing_order_items: List, kitchen_queue: List) -> Dict:
    """Analyze coordination requirements for multi-item orders"""
    
    order_id = item_data['order_id']
    current_prep_time = item_data['estimated_prep_time']
    
    if not existing_order_items:
        return {
            'strategy': 'single_item',
            'coordination_needed': False,
            'max_prep_time': current_prep_time,
            'items_in_order': 1
        }
    
    # Calculate order coordination requirements
    all_prep_times = [item.estimated_prep_time for item in existing_order_items] + [current_prep_time]
    max_prep_time = max(all_prep_times)
    stations_involved = set([item.kitchen_station_id for item in existing_order_items] + [item_data['kitchen_station_id']])
    
    return {
        'strategy': 'multi_item_coordination',
        'coordination_needed': True,
        'max_prep_time': max_prep_time,
        'items_in_order': len(existing_order_items) + 1,
        'stations_involved': len(stations_involved),
        'is_critical_path': current_prep_time == max_prep_time,
        'coordination_delay': max(0, max_prep_time - current_prep_time)
    }

def determine_sequence_position(item_data: Dict, coordination_analysis: Dict, kitchen_queue: List) -> Dict:
    """Determine optimal sequence position with clear reasoning"""
    
    order_id = item_data['order_id']
    prep_time = item_data['estimated_prep_time']
    priority = item_data.get('priority', 'medium')
    
    priority_values = {'urgent': 4, 'high': 3, 'medium': 2, 'low': 1}
    item_priority_value = priority_values.get(priority, 2)
    
    if not coordination_analysis['coordination_needed']:
        # Single item - position based on priority
        position = 1
        for queued_item in kitchen_queue:
            queued_priority = priority_values.get(queued_item.priority, 2)
            if item_priority_value <= queued_priority:
                position += 1
        
        reasoning = f"Single item order. Positioned #{position} based on {priority} priority."
        
    else:
        # Multi-item order coordination
        if coordination_analysis['is_critical_path']:
            # Critical path item starts first
            position = 1
            for queued_item in kitchen_queue:
                if queued_item.order_id != order_id:
                    queued_priority = priority_values.get(queued_item.priority, 2)
                    if item_priority_value > queued_priority:
                        break
                position += 1
            
            reasoning = f"Critical path item for order {order_id} ({prep_time}min). Must start first to coordinate with {coordination_analysis['items_in_order']-1} other items. Other items will start {coordination_analysis['coordination_delay']}min later."
            
        else:
            # Non-critical item - delayed start
            delay_minutes = coordination_analysis['coordination_delay']
            position = len(kitchen_queue) + 1
            
            reasoning = f"Supporting item for order {order_id}. Delayed {delay_minutes} minutes to synchronize with critical path item. Ensures complete order ready simultaneously."
    
    return {
        'sequence_number': position,
        'reasoning': reasoning,
        'coordination_delay': coordination_analysis.get('coordination_delay', 0)
    }

@router.post("/items/sequenced")
async def create_sequenced_item(
    item_data: dict,
    db: Session = Depends(get_db)
):
    """Create order item with intelligent sequencing"""
    
    try:
        # Validate kitchen station
        kitchen = db.query(KitchenStation).filter(
            KitchenStation.id == item_data["kitchen_station_id"]
        ).first()
        
        if not kitchen:
            raise HTTPException(status_code=404, detail="Kitchen station not found")
        
        # Get existing items for this order
        existing_order_items = db.query(OrderItem).filter(
            OrderItem.order_id == item_data["order_id"]
        ).all()
        
        # Get current kitchen queue
        kitchen_queue = db.query(OrderItem).filter(
            OrderItem.kitchen_station_id == item_data["kitchen_station_id"],
            OrderItem.status.in_([ItemStatus.NEW, ItemStatus.IN_PREPARATION])
        ).all()
        
        # Analyze coordination needs
        coordination_analysis = analyze_order_coordination(
            item_data, existing_order_items, kitchen_queue
        )
        
        # Determine sequence position
        sequence_result = determine_sequence_position(
            item_data, coordination_analysis, kitchen_queue
        )
        
        # Create order item
        new_item = OrderItem(
            order_id=item_data["order_id"],
            item_name=item_data["item_name"],
            kitchen_station_id=item_data["kitchen_station_id"],
            estimated_prep_time=item_data["estimated_prep_time"],
            priority=item_data.get("priority", "medium"),
            sequence_number=sequence_result['sequence_number'],
            ai_reasoning=sequence_result['reasoning']
        )
        
        db.add(new_item)
        db.commit()
        db.refresh(new_item)
        
        return {
            "message": "Item sequenced successfully",
            "item_id": new_item.id,
            "sequence_number": new_item.sequence_number,
            "reasoning": sequence_result['reasoning'],
            "coordination_delay": sequence_result['coordination_delay']
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create item: {str(e)}")

@router.post("/resequence/kitchen/{kitchen_id}")
async def resequence_kitchen(
    kitchen_id: str,
    db: Session = Depends(get_db)
):
    """Resequence kitchen queue with order coordination"""
    
    try:
        # Get all pending items
        items = db.query(OrderItem).filter(
            OrderItem.kitchen_station_id == kitchen_id,
            OrderItem.status.in_([ItemStatus.NEW, ItemStatus.IN_PREPARATION])
        ).all()
        
        if not items:
            return {"message": "No items to resequence", "items_resequenced": 0}
        
        # Group by order
        orders_map = {}
        for item in items:
            if item.order_id not in orders_map:
                orders_map[item.order_id] = []
            orders_map[item.order_id].append(item)
        
        # Resequence logic
        sequenced_items = []
        sequence_number = 1
        
        # Handle critical path items first (longest prep time in multi-item orders)
        for order_id, order_items in orders_map.items():
            if len(order_items) > 1:
                critical_item = max(order_items, key=lambda x: x.estimated_prep_time)
                critical_item.sequence_number = sequence_number
                critical_item.ai_reasoning = f"Critical path for order {order_id}. Longest prep time ({critical_item.estimated_prep_time}min) starts first."
                sequenced_items.append(critical_item)
                sequence_number += 1
        
        # Handle remaining items
        remaining_items = []
        for order_id, order_items in orders_map.items():
            if len(order_items) == 1:
                item = order_items[0]
                remaining_items.append((item, f"Single item order. Priority: {item.priority}"))
            else:
                critical_prep_time = max(item.estimated_prep_time for item in order_items)
                for item in order_items:
                    if item not in sequenced_items:
                        delay = critical_prep_time - item.estimated_prep_time
                        remaining_items.append((item, f"Coordinated with order {order_id}. Delayed {delay}min to finish together."))
        
        # Sort by priority
        priority_order = {'urgent': 4, 'high': 3, 'medium': 2, 'low': 1}
        remaining_items.sort(key=lambda x: priority_order.get(x[0].priority, 2), reverse=True)
        
        # Assign sequence numbers
        for item, reasoning in remaining_items:
            item.sequence_number = sequence_number
            item.ai_reasoning = reasoning
            sequenced_items.append(item)
            sequence_number += 1
        
        db.commit()
        
        return {
            "message": f"Resequenced {len(items)} items with coordination",
            "items_resequenced": len(items),
            "multi_item_orders": len([o for o in orders_map.values() if len(o) > 1]),
            "sequence": [
                {
                    "item_id": item.id,
                    "sequence": item.sequence_number,
                    "item_name": item.item_name,
                    "order_id": item.order_id,
                    "reasoning": item.ai_reasoning
                }
                for item in sequenced_items
            ]
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to resequence: {str(e)}")