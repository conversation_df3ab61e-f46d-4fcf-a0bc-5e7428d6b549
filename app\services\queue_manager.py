import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import redis.asyncio as redis
import json

from ..models.order_item import OrderItem, ItemStatus
from ..models.kitchen_station import KitchenStation
from ..models.completed_order import CompletedOrder
# Removed AI engine dependency

class QueueManager:
    def __init__(self, db_session: Session, redis_client: redis.Redis):
        self.db = db_session
        self.redis = redis_client
        # Simplified queue management without AI engine
        self.auto_transition_task = None
        
    async def add_item_to_queue(self, item_data: Dict) -> Dict:
        """Add new item to queue and trigger AI sequencing"""
        
        # Create new order item
        new_item = OrderItem(**item_data)
        self.db.add(new_item)
        self.db.commit()
        self.db.refresh(new_item)
        
        # Simple sequencing - assign next sequence number
        max_sequence = self.db.query(OrderItem).filter(
            and_(
                OrderItem.kitchen_station_id == new_item.kitchen_station_id,
                OrderItem.status == ItemStatus.NEW
            )
        ).count()
        new_item.sequence_number = max_sequence
        self.db.commit()
        
        # Cache in Redis for fast access
        await self._cache_item(new_item)
        
        return {
            "id": new_item.id,
            "sequence_number": new_item.sequence_number,
            "ai_reasoning": new_item.ai_reasoning,
            "status": "added_to_queue"
        }
    
    async def update_item_status(self, item_id: int, new_status: ItemStatus) -> Dict:
        """Update item status and handle automatic transitions with proper timing"""
        
        item = self.db.query(OrderItem).filter(OrderItem.id == item_id).first()
        if not item:
            return {"error": "Item not found"}
        
        old_status = item.status
        item.status = new_status
        
        # Update timing fields based on status transitions
        now = datetime.utcnow()
        
        if new_status == ItemStatus.IN_PREPARATION and old_status == ItemStatus.NEW:
            # Starting preparation - record start time
            item.preparation_start_time = now
            item.sequence_number = None  # Remove from sequencing
            await self._update_kitchen_load(item.kitchen_station_id, 1)
            
        elif new_status == ItemStatus.READY and old_status == ItemStatus.IN_PREPARATION:
            # Item is ready - record ready time
            item.ready_time = now
            
        elif new_status == ItemStatus.SERVED and old_status == ItemStatus.READY:
            # Item served - record served time and decrease kitchen load
            item.served_time = now
            await self._update_kitchen_load(item.kitchen_station_id, -1)
            
            # Check if this completes the entire order
            await self._check_order_completion(item.order_id)
        
        self.db.commit()
        
        # If item moved from NEW to IN_PREPARATION, resequence remaining items
        if old_status == ItemStatus.NEW and new_status == ItemStatus.IN_PREPARATION:
            await self._resequence_remaining_items(item.kitchen_station_id)
        
        # Update cache
        await self._cache_item(item)
        
        # Calculate timing metrics for completed items
        timing_info = {}
        if item.served_time and item.preparation_start_time:
            total_prep_time = (item.served_time - item.preparation_start_time).total_seconds() / 60
            wait_time = (item.preparation_start_time - item.created_at).total_seconds() / 60
            timing_info = {
                "total_prep_time_minutes": round(total_prep_time, 2),
                "wait_time_minutes": round(wait_time, 2),
                "estimated_vs_actual": round(total_prep_time - item.estimated_prep_time, 2)
            }
        
        return {
            "id": item.id,
            "old_status": old_status.value,
            "new_status": new_status.value,
            "updated_at": now.isoformat(),
            "timing_info": timing_info
        }
    
    async def get_kitchen_queue(self, kitchen_station_id: str, status_filter: Optional[ItemStatus] = None) -> List[Dict]:
        """Get current queue for a specific kitchen station"""
        
        query = self.db.query(OrderItem).filter(OrderItem.kitchen_station_id == kitchen_station_id)
        
        if status_filter:
            query = query.filter(OrderItem.status == status_filter)
        else:
            # Default: show items that are in queue (NEW), being prepared, or ready
            query = query.filter(or_(
                OrderItem.status == ItemStatus.NEW,
                OrderItem.status == ItemStatus.IN_PREPARATION,
                OrderItem.status == ItemStatus.READY
            ))
        
        items = query.order_by(OrderItem.sequence_number.asc().nullslast()).all()
        
        return [self._item_to_dict(item) for item in items]
    
    async def get_all_queues(self) -> Dict[str, Dict]:
        """Get queues for all kitchen stations with station metadata"""
        
        # Get all active kitchen stations
        stations = self.db.query(KitchenStation).filter(KitchenStation.is_active == True).all()
        
        queues = {}
        for station in stations:
            queue_items = await self.get_kitchen_queue(station.id)
            queues[station.id] = {
                "station_name": station.name,
                "max_capacity": station.max_capacity,
                "current_load": station.current_load,
                "queue_length": len(queue_items),
                "items": queue_items
            }
        
        return queues
    
    async def _resequence_remaining_items(self, kitchen_station_id: str):
        """Resequence remaining NEW items for a specific kitchen"""
        
        # Get all NEW items for this kitchen
        new_items = self.db.query(OrderItem).filter(
            and_(
                OrderItem.kitchen_station_id == kitchen_station_id,
                OrderItem.status == ItemStatus.NEW
            )
        ).order_by(OrderItem.created_at.asc()).all()
        
        # Simple FIFO resequencing
        for i, item in enumerate(new_items, 1):
            item.sequence_number = i
        
        self.db.commit()
        
        # Update cache
        for item in new_items:
            await self._cache_item(item)
    
    async def _update_kitchen_load(self, kitchen_station_id: str, change: int):
        """Update kitchen station current load"""
        
        kitchen = self.db.query(KitchenStation).filter(KitchenStation.id == kitchen_station_id).first()
        if kitchen:
            kitchen.current_load = max(0, kitchen.current_load + change)
            kitchen.last_updated = datetime.utcnow()
            self.db.commit()
    
    async def _cache_item(self, item: OrderItem):
        """Cache item data in Redis for fast access"""
        
        item_data = self._item_to_dict(item)
        cache_key = f"item:{item.id}"
        await self.redis.setex(cache_key, 3600, json.dumps(item_data, default=str))
    
    def _item_to_dict(self, item: OrderItem) -> Dict:
        """Convert OrderItem to dictionary"""
        
        return {
            'id': item.id,
            'order_id': item.order_id,
            'item_name': item.item_name,
            'kitchen_station_id': item.kitchen_station_id,
            'status': item.status.value,
            'priority': item.priority.value,
            'sequence_number': item.sequence_number,
            'ai_reasoning': item.ai_reasoning,
            'estimated_prep_time': item.estimated_prep_time,
            'created_at': item.created_at,
            'preparation_start_time': item.preparation_start_time,
            'ready_time': item.ready_time,
            'served_time': item.served_time,
            'item_metadata': item.item_metadata,
            'customer_preferences': item.customer_preferences
        }
    
    async def start_auto_transitions(self):
        """Start background task for automatic status transitions"""
        
        if self.auto_transition_task is None:
            self.auto_transition_task = asyncio.create_task(self._auto_transition_loop())
    
    async def stop_auto_transitions(self):
        """Stop background task for automatic status transitions"""
        
        if self.auto_transition_task:
            self.auto_transition_task.cancel()
            self.auto_transition_task = None
    
    async def _auto_transition_loop(self):
        """Background loop to handle automatic status transitions"""
        
        while True:
            try:
                await self._check_preparation_timeouts()
                await self._check_ready_timeouts()
                await asyncio.sleep(30)  # Check every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Auto transition error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _check_preparation_timeouts(self):
        """Check for items that should be ready based on estimated prep time"""
        
        cutoff_time = datetime.utcnow() - timedelta(minutes=5)  # 5 minute buffer
        
        items = self.db.query(OrderItem).filter(
            and_(
                OrderItem.status == ItemStatus.IN_PREPARATION,
                OrderItem.preparation_start_time < cutoff_time
            )
        ).all()
        
        for item in items:
            if item.preparation_start_time:
                elapsed = (datetime.utcnow() - item.preparation_start_time).total_seconds() / 60
                if elapsed >= item.estimated_prep_time:
                    # Auto-transition to READY (could be configurable)
                    await self.update_item_status(item.id, ItemStatus.READY)
    
    async def _check_ready_timeouts(self):
        """Check for items that have been ready too long"""
        
        cutoff_time = datetime.utcnow() - timedelta(minutes=10)  # 10 minute threshold
        
        items = self.db.query(OrderItem).filter(
            and_(
                OrderItem.status == ItemStatus.READY,
                OrderItem.ready_time < cutoff_time
            )
        ).all()
        
        # Log or alert for items ready too long (implementation specific)
        for item in items:
            print(f"Alert: Item {item.id} ({item.item_name}) has been ready for too long")
    
    async def get_performance_metrics(self, kitchen_station_id: Optional[str] = None, 
                                    hours: int = 24) -> Dict:
        """Get performance metrics for analysis"""
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        query = self.db.query(OrderItem).filter(OrderItem.created_at >= cutoff_time)
        
        if kitchen_station_id:
            query = query.filter(OrderItem.kitchen_station_id == kitchen_station_id)
        
        items = query.all()
        
        metrics = {
            "total_items": len(items),
            "completed_items": len([i for i in items if i.status == ItemStatus.SERVED]),
            "average_prep_time": 0,
            "average_wait_time": 0,
            "status_distribution": {},
            "kitchen_performance": {}
        }
        
        if items:
            # Calculate timing metrics
            completed_items = [i for i in items if i.served_time and i.preparation_start_time]
            if completed_items:
                prep_times = [(i.ready_time - i.preparation_start_time).total_seconds() / 60 
                             for i in completed_items if i.ready_time]
                wait_times = [(i.preparation_start_time - i.created_at).total_seconds() / 60 
                             for i in completed_items]
                
                metrics["average_prep_time"] = sum(prep_times) / len(prep_times) if prep_times else 0
                metrics["average_wait_time"] = sum(wait_times) / len(wait_times) if wait_times else 0
            
            # Status distribution
            for item in items:
                status = item.status.value
                metrics["status_distribution"][status] = metrics["status_distribution"].get(status, 0) + 1
        
        return metrics
    
    async def _check_order_completion(self, order_id: str):
        """Check if all items in an order are completed and create completion record"""
        
        # Get all items for this order
        order_items = self.db.query(OrderItem).filter(OrderItem.order_id == order_id).all()
        
        if not order_items:
            return
        
        # Check if all items are served
        all_served = all(item.status == ItemStatus.SERVED for item in order_items)
        
        if all_served:
            # Check if completion record already exists
            existing = self.db.query(CompletedOrder).filter(CompletedOrder.order_id == order_id).first()
            if existing:
                return
            
            # Calculate order metrics
            order_created = min(item.created_at for item in order_items)
            order_completed = max(item.served_time for item in order_items if item.served_time)
            
            # Calculate timing metrics
            total_order_time = (order_completed - order_created).total_seconds() / 60
            
            # Calculate preparation time (from first start to last completion)
            prep_starts = [item.preparation_start_time for item in order_items if item.preparation_start_time]
            prep_ends = [item.served_time for item in order_items if item.served_time]
            
            if prep_starts and prep_ends:
                first_prep_start = min(prep_starts)
                last_completion = max(prep_ends)
                total_prep_time = (last_completion - first_prep_start).total_seconds() / 60
                total_wait_time = (first_prep_start - order_created).total_seconds() / 60
            else:
                total_prep_time = 0
                total_wait_time = total_order_time
            
            # Calculate estimated vs actual
            estimated_total = sum(item.estimated_prep_time for item in order_items)
            actual_vs_estimated = total_prep_time - estimated_total
            
            # Calculate efficiency score (1.0 = perfect, < 1.0 = slower than expected)
            efficiency_score = estimated_total / total_prep_time if total_prep_time > 0 else 1.0
            
            # Get unique kitchen stations used
            kitchen_stations = list(set(item.kitchen_station_id for item in order_items))
            
            # Create completion record
            completed_order = CompletedOrder(
                order_id=order_id,
                order_created_at=order_created,
                order_completed_at=order_completed,
                total_preparation_time=total_prep_time,
                total_wait_time=total_wait_time,
                total_items=len(order_items),
                kitchen_stations_used=kitchen_stations,
                estimated_total_time=estimated_total,
                actual_vs_estimated=actual_vs_estimated,
                efficiency_score=efficiency_score,
                order_metadata={
                    "items": [{
                        "item_name": item.item_name,
                        "kitchen_station": item.kitchen_station_id,
                        "estimated_prep_time": item.estimated_prep_time,
                        "actual_prep_time": (item.served_time - item.preparation_start_time).total_seconds() / 60 if item.served_time and item.preparation_start_time else None
                    } for item in order_items]
                }
            )
            
            self.db.add(completed_order)
            self.db.commit()
            
            print(f"Order {order_id} completed - Total time: {total_order_time:.1f}min, Efficiency: {efficiency_score:.2f}")