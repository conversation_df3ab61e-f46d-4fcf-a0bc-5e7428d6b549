import os
from dotenv import load_dotenv

load_dotenv()


class Settings:
    # Database
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "postgresql://kds_user:kds_password@localhost:5432/kds_sequencing",
    )

    # Redis
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")

    # Ollama LLM
    OLLAMA_HOST: str = os.getenv("OLLAMA_HOST", "http://************:11434")
    OLLAMA_MODEL: str = os.getenv("OLLAMA_MODEL", "llama3.2")

    # AI Configuration
    AI_MODEL_UPDATE_INTERVAL: int = int(os.getenv("AI_MODEL_UPDATE_INTERVAL", "3600"))
    MIN_TRAINING_SAMPLES: int = int(os.getenv("MIN_TRAINING_SAMPLES", "50"))

    # Service
    SERVICE_PORT: int = int(os.getenv("SERVICE_PORT", "8000"))
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "info")
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")


settings = Settings()
