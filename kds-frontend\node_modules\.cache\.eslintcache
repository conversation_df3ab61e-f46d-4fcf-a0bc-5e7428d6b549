[{"d:\\kds_ai_sequencing\\kds-frontend\\src\\index.js": "1", "d:\\kds_ai_sequencing\\kds-frontend\\src\\App.js": "2", "D:\\kds_ai_sequencing\\kds-frontend\\src\\index.js": "3", "D:\\kds_ai_sequencing\\kds-frontend\\src\\App.js": "4", "D:\\kds_ai_sequencing\\kds-frontend\\src\\components\\KitchenManager.js": "5", "D:\\kds_ai_sequencing\\kds-frontend\\src\\components\\OrderOverview.js": "6"}, {"size": 182, "mtime": 1757321827858, "results": "7", "hashOfConfig": "8"}, {"size": 7982, "mtime": 1757325512294, "results": "9", "hashOfConfig": "8"}, {"size": 182, "mtime": 1757321827858, "results": "10", "hashOfConfig": "11"}, {"size": 566, "mtime": 1757327522708, "results": "12", "hashOfConfig": "11"}, {"size": 9394, "mtime": 1757328795994, "results": "13", "hashOfConfig": "11"}, {"size": 12024, "mtime": 1757329231506, "results": "14", "hashOfConfig": "11"}, {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13f34st", {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ah7tze", {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d:\\kds_ai_sequencing\\kds-frontend\\src\\index.js", [], [], "d:\\kds_ai_sequencing\\kds-frontend\\src\\App.js", [], [], "D:\\kds_ai_sequencing\\kds-frontend\\src\\index.js", [], [], "D:\\kds_ai_sequencing\\kds-frontend\\src\\App.js", [], [], "D:\\kds_ai_sequencing\\kds-frontend\\src\\components\\KitchenManager.js", ["33", "34"], [], "D:\\kds_ai_sequencing\\kds-frontend\\src\\components\\OrderOverview.js", [], [], {"ruleId": "35", "severity": 1, "message": "36", "line": 9, "column": 10, "nodeType": "37", "messageId": "38", "endLine": 9, "endColumn": 25}, {"ruleId": "39", "severity": 1, "message": "40", "line": 15, "column": 6, "nodeType": "41", "endLine": 15, "endColumn": 8, "suggestions": "42"}, "no-unused-vars", "'kitchenStations' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["43"], {"desc": "44", "fix": "45"}, "Update the dependencies array to be: [fetchData]", {"range": "46", "text": "47"}, [482, 484], "[fetchData]"]