import React, { useState, useEffect } from 'react';
import axios from 'axios';

const API_BASE = 'http://localhost:8000/api/v1';

function KitchenManager({ onSwitchToOrders }) {
  const [queues, setQueues] = useState({});
  const [selectedKitchen, setSelectedKitchen] = useState('');
  const [kitchenStations, setKitchenStations] = useState([]);

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 2000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    try {
      const [queuesRes, stationsRes] = await Promise.all([
        axios.get(`${API_BASE}/queue/all`),
        axios.get(`${API_BASE}/kitchen-stations`)
      ]);
      const newQueues = queuesRes.data.queues || {};
      setQueues(newQueues);
      setKitchenStations(stationsRes.data || []);
      
      // Only set initial selection if no kitchen is selected AND this is the first load
      if (!selectedKitchen && Object.keys(newQueues).length > 0) {
        setSelectedKitchen(Object.keys(newQueues)[0]);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const updateItemStatus = async (itemId, status) => {
    try {
      await axios.put(`${API_BASE}/items/${itemId}/status/simple`, { status });
      fetchData();
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const resequenceKitchen = async (kitchenId) => {
    try {
      await axios.post(`${API_BASE}/resequence/ai/${kitchenId}`);
      fetchData();
    } catch (error) {
      console.error('Error resequencing:', error);
    }
  };

  const getLoadPercentage = (current, max) => {
    return Math.round((current / max) * 100);
  };

  const getLoadColor = (percentage) => {
    if (percentage > 80) return '#f44336';
    if (percentage > 60) return '#ff9800';
    return '#4caf50';
  };

  const selectedQueue = queues[selectedKitchen];

  return (
    <div className="kitchen-manager">
      <div className="manager-header">
        <h1>🍳 Kitchen Manager Dashboard</h1>
        <button 
          onClick={onSwitchToOrders}
          className="switch-mode-btn"
        >
          📋 Switch to Order Overview
        </button>
      </div>

      <div className="kitchen-layout">
        {/* Kitchen Selection Sidebar */}
        <div className="kitchen-sidebar">
          <h2>Kitchen Stations</h2>
          <div className="stations-list">
            {Object.entries(queues).map(([kitchenId, queueData]) => {
              const loadPercentage = getLoadPercentage(queueData.current_load, queueData.max_capacity);
              return (
                <div
                  key={kitchenId}
                  className={`station-card ${selectedKitchen === kitchenId ? 'selected' : ''}`}
                  onClick={() => setSelectedKitchen(kitchenId)}
                >
                  <div className="station-header">
                    <h3>{queueData.station_name}</h3>
                    <div 
                      className="load-indicator"
                      style={{ backgroundColor: getLoadColor(loadPercentage) }}
                    >
                      {loadPercentage}%
                    </div>
                  </div>
                  <div className="station-stats">
                    <span>📋 {queueData.queue_length} items</span>
                    <span>⚡ {queueData.current_load}/{queueData.max_capacity}</span>
                  </div>
                  {queueData.queue_length > 0 && (
                    <div className="next-item">
                      Next: {queueData.items[0]?.item_name}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Selected Kitchen Details */}
        <div className="kitchen-details">
          {selectedQueue ? (
            <>
              <div className="kitchen-header">
                <div className="kitchen-info">
                  <h2>{selectedQueue.station_name}</h2>
                  <div className="kitchen-metrics">
                    <div className="metric">
                      <span className="metric-label">Queue Length</span>
                      <span className="metric-value">{selectedQueue.queue_length}</span>
                    </div>
                    <div className="metric">
                      <span className="metric-label">Capacity</span>
                      <span className="metric-value">
                        {selectedQueue.current_load}/{selectedQueue.max_capacity}
                      </span>
                    </div>
                    <div className="metric">
                      <span className="metric-label">Load</span>
                      <span 
                        className="metric-value"
                        style={{ 
                          color: getLoadColor(getLoadPercentage(selectedQueue.current_load, selectedQueue.max_capacity))
                        }}
                      >
                        {getLoadPercentage(selectedQueue.current_load, selectedQueue.max_capacity)}%
                      </span>
                    </div>
                  </div>
                </div>
                <button 
                  onClick={() => resequenceKitchen(selectedKitchen)}
                  className="resequence-btn"
                  disabled={selectedQueue.queue_length === 0}
                >
                  🤖 AI Resequence
                </button>
              </div>

              <div className="queue-items">
                {selectedQueue.items.length === 0 ? (
                  <div className="empty-queue">
                    <h3>🎉 No items in queue</h3>
                    <p>This kitchen is ready for new orders!</p>
                  </div>
                ) : (
                  selectedQueue.items.map((item, index) => (
                    <div key={item.id} className={`queue-item-card ${item.status}`}>
                      <div className="item-sequence">
                        <div className="sequence-number">#{item.sequence_number}</div>
                        <div className="sequence-indicator">
                          {index === 0 && item.status === 'new' && (
                            <span className="next-badge">NEXT</span>
                          )}
                        </div>
                      </div>

                      <div className="item-content">
                        <div className="item-main">
                          <h3 className="item-name">{item.item_name}</h3>
                          <div className="item-meta">
                            <span className="order-id">Order: {item.order_id}</span>
                            <span className="prep-time">🕒 {item.estimated_prep_time}min</span>
                            <span className={`priority ${item.priority}`}>
                              {item.priority.toUpperCase()}
                            </span>
                          </div>
                        </div>

                        <div className="item-status-section">
                          <div className={`status-badge ${item.status}`}>
                            {item.status.replace('_', ' ').toUpperCase()}
                          </div>
                          
                          <div className="status-actions">
                            {item.status === 'new' && (
                              <button 
                                onClick={() => updateItemStatus(item.id, 'in_preparation')}
                                className="status-btn start"
                              >
                                ▶️ Start Cooking
                              </button>
                            )}
                            {item.status === 'in_preparation' && (
                              <button 
                                onClick={() => updateItemStatus(item.id, 'ready')}
                                className="status-btn ready"
                              >
                                ✅ Mark Ready
                              </button>
                            )}
                            {item.status === 'ready' && (
                              <button 
                                onClick={() => updateItemStatus(item.id, 'served')}
                                className="status-btn served"
                              >
                                🍽️ Served
                              </button>
                            )}
                          </div>
                        </div>
                      </div>

                      {item.ai_reasoning && (
                        <div className="ai-reasoning">
                          <strong>🧠 AI Reasoning:</strong>
                          <p>{item.ai_reasoning}</p>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </>
          ) : (
            <div className="no-kitchen-selected">
              <h2>Select a kitchen station to manage</h2>
              <p>Choose a kitchen from the sidebar to view and manage its queue.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default KitchenManager;