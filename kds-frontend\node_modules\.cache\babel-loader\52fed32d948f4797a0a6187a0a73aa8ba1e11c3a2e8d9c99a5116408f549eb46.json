{"ast": null, "code": "var _jsxFileName = \"D:\\\\kds_ai_sequencing\\\\kds-frontend\\\\src\\\\components\\\\OrderOverview.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE = 'http://localhost:8000/api/v1';\nfunction OrderOverview({\n  onSwitchToKitchen\n}) {\n  _s();\n  const [orders, setOrders] = useState({});\n  const [completedOrders, setCompletedOrders] = useState([]);\n  const [newOrder, setNewOrder] = useState({\n    order_id: '',\n    items: []\n  });\n  const [menuItems, setMenuItems] = useState([]);\n  const [selectedItem, setSelectedItem] = useState('');\n  const [activeTab, setActiveTab] = useState('active');\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 3000);\n    return () => clearInterval(interval);\n  }, []);\n  const fetchData = async () => {\n    try {\n      const [queuesRes, menuRes, completedRes] = await Promise.all([axios.get(`${API_BASE}/queue/all`), axios.get(`${API_BASE}/menu-items`), axios.get(`${API_BASE}/orders/completed?limit=20&hours=24`)]);\n\n      // Group items by order_id\n      const orderMap = {};\n      Object.values(queuesRes.data.queues || {}).forEach(queue => {\n        queue.items.forEach(item => {\n          if (!orderMap[item.order_id]) {\n            orderMap[item.order_id] = {\n              order_id: item.order_id,\n              items: [],\n              status: 'pending',\n              total_prep_time: 0,\n              stations: new Set()\n            };\n          }\n          orderMap[item.order_id].items.push({\n            ...item,\n            kitchen_name: queue.station_name\n          });\n          orderMap[item.order_id].total_prep_time = Math.max(orderMap[item.order_id].total_prep_time, item.estimated_prep_time);\n          orderMap[item.order_id].stations.add(queue.station_name);\n        });\n      });\n\n      // Convert stations Set to Array\n      Object.values(orderMap).forEach(order => {\n        order.stations = Array.from(order.stations);\n        order.status = order.items.every(item => item.status === 'served') ? 'completed' : order.items.some(item => item.status === 'ready') ? 'ready' : order.items.some(item => item.status === 'in_preparation') ? 'preparing' : 'pending';\n      });\n      setOrders(orderMap);\n      setMenuItems(menuRes.data || []);\n      setCompletedOrders(completedRes.data || []);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    }\n  };\n  const addItemToOrder = () => {\n    if (!selectedItem || !newOrder.order_id) return;\n    const menuItem = menuItems.find(item => item.id === selectedItem);\n    if (menuItem) {\n      setNewOrder(prev => ({\n        ...prev,\n        items: [...prev.items, {\n          id: Date.now(),\n          menu_item_id: menuItem.id,\n          item_name: menuItem.name,\n          kitchen_station_id: menuItem.kitchen_station_id,\n          estimated_prep_time: menuItem.standard_prep_time,\n          priority: 'medium'\n        }]\n      }));\n      setSelectedItem('');\n    }\n  };\n  const removeItemFromOrder = itemId => {\n    setNewOrder(prev => ({\n      ...prev,\n      items: prev.items.filter(item => item.id !== itemId)\n    }));\n  };\n  const submitOrder = async () => {\n    if (!newOrder.order_id || newOrder.items.length === 0) return;\n    try {\n      for (const item of newOrder.items) {\n        await axios.post(`${API_BASE}/items/ai-sequenced`, {\n          order_id: newOrder.order_id,\n          item_name: item.item_name,\n          kitchen_station_id: item.kitchen_station_id,\n          estimated_prep_time: item.estimated_prep_time,\n          priority: item.priority\n        });\n      }\n      setNewOrder({\n        order_id: '',\n        items: []\n      });\n      fetchData();\n    } catch (error) {\n      console.error('Error creating order:', error);\n    }\n  };\n  const getOrderStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return '#4caf50';\n      case 'ready':\n        return '#ff9800';\n      case 'preparing':\n        return '#2196f3';\n      default:\n        return '#9e9e9e';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"order-overview\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overview-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCB Order Management Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onSwitchToKitchen,\n        className: \"switch-mode-btn\",\n        children: \"\\uD83C\\uDF73 Switch to Kitchen Manager\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"new-order-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Create New Order\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Order ID (e.g., ORD-001)\",\n          value: newOrder.order_id,\n          onChange: e => setNewOrder(prev => ({\n            ...prev,\n            order_id: e.target.value\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"item-selector\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedItem,\n            onChange: e => setSelectedItem(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Menu Item\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), menuItems.map(item => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: item.id,\n              children: [item.name, \" (\", item.standard_prep_time, \"min)\"]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: addItemToOrder,\n            disabled: !selectedItem,\n            children: \"Add Item\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), newOrder.items.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-items\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Order Items:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this), newOrder.items.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [item.item_name, \" (\", item.estimated_prep_time, \"min)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: item.priority,\n              onChange: e => {\n                setNewOrder(prev => ({\n                  ...prev,\n                  items: prev.items.map(i => i.id === item.id ? {\n                    ...i,\n                    priority: e.target.value\n                  } : i)\n                }));\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"low\",\n                children: \"Low\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"medium\",\n                children: \"Medium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"high\",\n                children: \"High\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"urgent\",\n                children: \"Urgent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => removeItemFromOrder(item.id),\n              children: \"\\u274C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 19\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: submitOrder,\n            className: \"submit-order-btn\",\n            children: \"\\uD83E\\uDD16 Create Order with AI Sequencing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"orders-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"orders-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-btn ${activeTab === 'active' ? 'active' : ''}`,\n            onClick: () => setActiveTab('active'),\n            children: [\"\\uD83D\\uDCCB Active Orders (\", Object.keys(orders).length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-btn ${activeTab === 'completed' ? 'active' : ''}`,\n            onClick: () => setActiveTab('completed'),\n            children: [\"\\u2705 Completed Orders (\", completedOrders.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"orders-container\",\n        children: activeTab === 'active' ? Object.values(orders).map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: order.order_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"order-status\",\n              style: {\n                backgroundColor: getOrderStatusColor(order.status)\n              },\n              children: order.status.toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"order-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\uD83D\\uDCE6 \", order.items.length, \" items\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\uD83D\\uDD52 \", order.total_prep_time, \"min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\uD83C\\uDFEA \", order.stations.length, \" stations\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stations-list\",\n              children: order.stations.map(station => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"station-tag\",\n                children: station\n              }, station, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-items-list\",\n            children: order.items.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `item-row ${item.status}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"item-name\",\n                  children: item.item_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"item-kitchen\",\n                  children: item.kitchen_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sequence\",\n                  children: [\"#\", item.sequence_number]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `status ${item.status}`,\n                  children: item.status.replace('_', ' ').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 23\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 17\n          }, this), order.status === 'ready' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"complete-order-btn\",\n              children: \"\\u2705 Complete Order\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 19\n          }, this)]\n        }, order.order_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 15\n        }, this)) : completedOrders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-card completed\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: order.order_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"completion-badge\",\n              children: \"\\u2705 COMPLETED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"completion-metrics\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-label\",\n                children: \"Total Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-value\",\n                children: [order.total_preparation_time.toFixed(1), \"min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-label\",\n                children: \"Wait Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-value\",\n                children: [order.total_wait_time.toFixed(1), \"min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-label\",\n                children: \"Efficiency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `metric-value ${order.efficiency_score >= 1 ? 'good' : 'needs-improvement'}`,\n                children: [(order.efficiency_score * 100).toFixed(0), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\uD83D\\uDCE6 \", order.total_items, \" items\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\uD83C\\uDFEA \", order.kitchen_stations_used.length, \" stations\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: order.actual_vs_estimated > 0 ? 'over-time' : 'under-time',\n                children: [order.actual_vs_estimated > 0 ? '+' : '', order.actual_vs_estimated.toFixed(1), \"min vs estimate\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"completion-time\",\n              children: [\"Completed: \", new Date(order.order_completed_at).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this)]\n        }, order.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n}\n_s(OrderOverview, \"6dYBs1/o84a1HOGT9aA4f5xj+wA=\");\n_c = OrderOverview;\nexport default OrderOverview;\nvar _c;\n$RefreshReg$(_c, \"OrderOverview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "API_BASE", "OrderOverview", "onSwitchToKitchen", "_s", "orders", "setOrders", "completedOrders", "setCompletedOrders", "newOrder", "setNewOrder", "order_id", "items", "menuItems", "setMenuItems", "selectedItem", "setSelectedItem", "activeTab", "setActiveTab", "fetchData", "interval", "setInterval", "clearInterval", "queuesRes", "menuRes", "completedRes", "Promise", "all", "get", "orderMap", "Object", "values", "data", "queues", "for<PERSON>ach", "queue", "item", "status", "total_prep_time", "stations", "Set", "push", "kitchen_name", "station_name", "Math", "max", "estimated_prep_time", "add", "order", "Array", "from", "every", "some", "error", "console", "addItemToOrder", "menuItem", "find", "id", "prev", "Date", "now", "menu_item_id", "item_name", "name", "kitchen_station_id", "standard_prep_time", "priority", "removeItemFromOrder", "itemId", "filter", "submitOrder", "length", "post", "getOrderStatusColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "map", "disabled", "i", "keys", "style", "backgroundColor", "toUpperCase", "station", "sequence_number", "replace", "total_preparation_time", "toFixed", "total_wait_time", "efficiency_score", "total_items", "kitchen_stations_used", "actual_vs_estimated", "order_completed_at", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["D:/kds_ai_sequencing/kds-frontend/src/components/OrderOverview.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst API_BASE = 'http://localhost:8000/api/v1';\n\nfunction OrderOverview({ onSwitchToKitchen }) {\n  const [orders, setOrders] = useState({});\n  const [completedOrders, setCompletedOrders] = useState([]);\n  const [newOrder, setNewOrder] = useState({\n    order_id: '',\n    items: []\n  });\n  const [menuItems, setMenuItems] = useState([]);\n  const [selectedItem, setSelectedItem] = useState('');\n  const [activeTab, setActiveTab] = useState('active');\n\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 3000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      const [queuesRes, menuRes, completedRes] = await Promise.all([\n        axios.get(`${API_BASE}/queue/all`),\n        axios.get(`${API_BASE}/menu-items`),\n        axios.get(`${API_BASE}/orders/completed?limit=20&hours=24`)\n      ]);\n      \n      // Group items by order_id\n      const orderMap = {};\n      Object.values(queuesRes.data.queues || {}).forEach(queue => {\n        queue.items.forEach(item => {\n          if (!orderMap[item.order_id]) {\n            orderMap[item.order_id] = {\n              order_id: item.order_id,\n              items: [],\n              status: 'pending',\n              total_prep_time: 0,\n              stations: new Set()\n            };\n          }\n          orderMap[item.order_id].items.push({\n            ...item,\n            kitchen_name: queue.station_name\n          });\n          orderMap[item.order_id].total_prep_time = Math.max(\n            orderMap[item.order_id].total_prep_time,\n            item.estimated_prep_time\n          );\n          orderMap[item.order_id].stations.add(queue.station_name);\n        });\n      });\n\n      // Convert stations Set to Array\n      Object.values(orderMap).forEach(order => {\n        order.stations = Array.from(order.stations);\n        order.status = order.items.every(item => item.status === 'served') ? 'completed' :\n                      order.items.some(item => item.status === 'ready') ? 'ready' :\n                      order.items.some(item => item.status === 'in_preparation') ? 'preparing' : 'pending';\n      });\n\n      setOrders(orderMap);\n      setMenuItems(menuRes.data || []);\n      setCompletedOrders(completedRes.data || []);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    }\n  };\n\n  const addItemToOrder = () => {\n    if (!selectedItem || !newOrder.order_id) return;\n    \n    const menuItem = menuItems.find(item => item.id === selectedItem);\n    if (menuItem) {\n      setNewOrder(prev => ({\n        ...prev,\n        items: [...prev.items, {\n          id: Date.now(),\n          menu_item_id: menuItem.id,\n          item_name: menuItem.name,\n          kitchen_station_id: menuItem.kitchen_station_id,\n          estimated_prep_time: menuItem.standard_prep_time,\n          priority: 'medium'\n        }]\n      }));\n      setSelectedItem('');\n    }\n  };\n\n  const removeItemFromOrder = (itemId) => {\n    setNewOrder(prev => ({\n      ...prev,\n      items: prev.items.filter(item => item.id !== itemId)\n    }));\n  };\n\n  const submitOrder = async () => {\n    if (!newOrder.order_id || newOrder.items.length === 0) return;\n\n    try {\n      for (const item of newOrder.items) {\n        await axios.post(`${API_BASE}/items/ai-sequenced`, {\n          order_id: newOrder.order_id,\n          item_name: item.item_name,\n          kitchen_station_id: item.kitchen_station_id,\n          estimated_prep_time: item.estimated_prep_time,\n          priority: item.priority\n        });\n      }\n      \n      setNewOrder({ order_id: '', items: [] });\n      fetchData();\n    } catch (error) {\n      console.error('Error creating order:', error);\n    }\n  };\n\n  const getOrderStatusColor = (status) => {\n    switch (status) {\n      case 'completed': return '#4caf50';\n      case 'ready': return '#ff9800';\n      case 'preparing': return '#2196f3';\n      default: return '#9e9e9e';\n    }\n  };\n\n  return (\n    <div className=\"order-overview\">\n      <div className=\"overview-header\">\n        <h1>📋 Order Management Dashboard</h1>\n        <button \n          onClick={onSwitchToKitchen}\n          className=\"switch-mode-btn\"\n        >\n          🍳 Switch to Kitchen Manager\n        </button>\n      </div>\n\n      {/* New Order Creation */}\n      <div className=\"new-order-section\">\n        <h2>Create New Order</h2>\n        <div className=\"order-form\">\n          <input\n            type=\"text\"\n            placeholder=\"Order ID (e.g., ORD-001)\"\n            value={newOrder.order_id}\n            onChange={(e) => setNewOrder(prev => ({ ...prev, order_id: e.target.value }))}\n          />\n          \n          <div className=\"item-selector\">\n            <select\n              value={selectedItem}\n              onChange={(e) => setSelectedItem(e.target.value)}\n            >\n              <option value=\"\">Select Menu Item</option>\n              {menuItems.map(item => (\n                <option key={item.id} value={item.id}>\n                  {item.name} ({item.standard_prep_time}min)\n                </option>\n              ))}\n            </select>\n            <button onClick={addItemToOrder} disabled={!selectedItem}>\n              Add Item\n            </button>\n          </div>\n\n          {newOrder.items.length > 0 && (\n            <div className=\"order-items\">\n              <h3>Order Items:</h3>\n              {newOrder.items.map(item => (\n                <div key={item.id} className=\"order-item\">\n                  <span>{item.item_name} ({item.estimated_prep_time}min)</span>\n                  <select\n                    value={item.priority}\n                    onChange={(e) => {\n                      setNewOrder(prev => ({\n                        ...prev,\n                        items: prev.items.map(i => \n                          i.id === item.id ? { ...i, priority: e.target.value } : i\n                        )\n                      }));\n                    }}\n                  >\n                    <option value=\"low\">Low</option>\n                    <option value=\"medium\">Medium</option>\n                    <option value=\"high\">High</option>\n                    <option value=\"urgent\">Urgent</option>\n                  </select>\n                  <button onClick={() => removeItemFromOrder(item.id)}>❌</button>\n                </div>\n              ))}\n              <button onClick={submitOrder} className=\"submit-order-btn\">\n                🤖 Create Order with AI Sequencing\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Orders Overview */}\n      <div className=\"orders-grid\">\n        <div className=\"orders-header\">\n          <div className=\"tab-buttons\">\n            <button \n              className={`tab-btn ${activeTab === 'active' ? 'active' : ''}`}\n              onClick={() => setActiveTab('active')}\n            >\n              📋 Active Orders ({Object.keys(orders).length})\n            </button>\n            <button \n              className={`tab-btn ${activeTab === 'completed' ? 'active' : ''}`}\n              onClick={() => setActiveTab('completed')}\n            >\n              ✅ Completed Orders ({completedOrders.length})\n            </button>\n          </div>\n        </div>\n        <div className=\"orders-container\">\n          {activeTab === 'active' ? (\n            Object.values(orders).map(order => (\n              <div key={order.order_id} className=\"order-card\">\n                <div className=\"order-header\">\n                  <h3>{order.order_id}</h3>\n                  <div \n                    className=\"order-status\"\n                    style={{ backgroundColor: getOrderStatusColor(order.status) }}\n                  >\n                    {order.status.toUpperCase()}\n                  </div>\n                </div>\n\n                <div className=\"order-info\">\n                  <div className=\"order-stats\">\n                    <span>📦 {order.items.length} items</span>\n                    <span>🕒 {order.total_prep_time}min</span>\n                    <span>🏪 {order.stations.length} stations</span>\n                  </div>\n                  <div className=\"stations-list\">\n                    {order.stations.map(station => (\n                      <span key={station} className=\"station-tag\">{station}</span>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"order-items-list\">\n                  {order.items.map(item => (\n                    <div key={item.id} className={`item-row ${item.status}`}>\n                      <div className=\"item-info\">\n                        <span className=\"item-name\">{item.item_name}</span>\n                        <span className=\"item-kitchen\">{item.kitchen_name}</span>\n                      </div>\n                      <div className=\"item-details\">\n                        <span className=\"sequence\">#{item.sequence_number}</span>\n                        <span className={`status ${item.status}`}>\n                          {item.status.replace('_', ' ').toUpperCase()}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                {order.status === 'ready' && (\n                  <div className=\"order-actions\">\n                    <button className=\"complete-order-btn\">\n                      ✅ Complete Order\n                    </button>\n                  </div>\n                )}\n              </div>\n            ))\n          ) : (\n            completedOrders.map(order => (\n              <div key={order.id} className=\"order-card completed\">\n                <div className=\"order-header\">\n                  <h3>{order.order_id}</h3>\n                  <div className=\"completion-badge\">✅ COMPLETED</div>\n                </div>\n\n                <div className=\"completion-metrics\">\n                  <div className=\"metric\">\n                    <span className=\"metric-label\">Total Time</span>\n                    <span className=\"metric-value\">{order.total_preparation_time.toFixed(1)}min</span>\n                  </div>\n                  <div className=\"metric\">\n                    <span className=\"metric-label\">Wait Time</span>\n                    <span className=\"metric-value\">{order.total_wait_time.toFixed(1)}min</span>\n                  </div>\n                  <div className=\"metric\">\n                    <span className=\"metric-label\">Efficiency</span>\n                    <span className={`metric-value ${order.efficiency_score >= 1 ? 'good' : 'needs-improvement'}`}>\n                      {(order.efficiency_score * 100).toFixed(0)}%\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"order-summary\">\n                  <div className=\"summary-stats\">\n                    <span>📦 {order.total_items} items</span>\n                    <span>🏪 {order.kitchen_stations_used.length} stations</span>\n                    <span className={order.actual_vs_estimated > 0 ? 'over-time' : 'under-time'}>\n                      {order.actual_vs_estimated > 0 ? '+' : ''}{order.actual_vs_estimated.toFixed(1)}min vs estimate\n                    </span>\n                  </div>\n                  <div className=\"completion-time\">\n                    Completed: {new Date(order.order_completed_at).toLocaleString()}\n                  </div>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default OrderOverview;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAG,8BAA8B;AAE/C,SAASC,aAAaA,CAAC;EAAEC;AAAkB,CAAC,EAAE;EAAAC,EAAA;EAC5C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACW,eAAe,EAAEC,kBAAkB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC;IACvCe,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,QAAQ,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACdsB,SAAS,CAAC,CAAC;IACX,MAAMC,QAAQ,GAAGC,WAAW,CAACF,SAAS,EAAE,IAAI,CAAC;IAC7C,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAM,CAACI,SAAS,EAAEC,OAAO,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3D7B,KAAK,CAAC8B,GAAG,CAAC,GAAG3B,QAAQ,YAAY,CAAC,EAClCH,KAAK,CAAC8B,GAAG,CAAC,GAAG3B,QAAQ,aAAa,CAAC,EACnCH,KAAK,CAAC8B,GAAG,CAAC,GAAG3B,QAAQ,qCAAqC,CAAC,CAC5D,CAAC;;MAEF;MACA,MAAM4B,QAAQ,GAAG,CAAC,CAAC;MACnBC,MAAM,CAACC,MAAM,CAACR,SAAS,CAACS,IAAI,CAACC,MAAM,IAAI,CAAC,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAC1DA,KAAK,CAACvB,KAAK,CAACsB,OAAO,CAACE,IAAI,IAAI;UAC1B,IAAI,CAACP,QAAQ,CAACO,IAAI,CAACzB,QAAQ,CAAC,EAAE;YAC5BkB,QAAQ,CAACO,IAAI,CAACzB,QAAQ,CAAC,GAAG;cACxBA,QAAQ,EAAEyB,IAAI,CAACzB,QAAQ;cACvBC,KAAK,EAAE,EAAE;cACTyB,MAAM,EAAE,SAAS;cACjBC,eAAe,EAAE,CAAC;cAClBC,QAAQ,EAAE,IAAIC,GAAG,CAAC;YACpB,CAAC;UACH;UACAX,QAAQ,CAACO,IAAI,CAACzB,QAAQ,CAAC,CAACC,KAAK,CAAC6B,IAAI,CAAC;YACjC,GAAGL,IAAI;YACPM,YAAY,EAAEP,KAAK,CAACQ;UACtB,CAAC,CAAC;UACFd,QAAQ,CAACO,IAAI,CAACzB,QAAQ,CAAC,CAAC2B,eAAe,GAAGM,IAAI,CAACC,GAAG,CAChDhB,QAAQ,CAACO,IAAI,CAACzB,QAAQ,CAAC,CAAC2B,eAAe,EACvCF,IAAI,CAACU,mBACP,CAAC;UACDjB,QAAQ,CAACO,IAAI,CAACzB,QAAQ,CAAC,CAAC4B,QAAQ,CAACQ,GAAG,CAACZ,KAAK,CAACQ,YAAY,CAAC;QAC1D,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAb,MAAM,CAACC,MAAM,CAACF,QAAQ,CAAC,CAACK,OAAO,CAACc,KAAK,IAAI;QACvCA,KAAK,CAACT,QAAQ,GAAGU,KAAK,CAACC,IAAI,CAACF,KAAK,CAACT,QAAQ,CAAC;QAC3CS,KAAK,CAACX,MAAM,GAAGW,KAAK,CAACpC,KAAK,CAACuC,KAAK,CAACf,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,QAAQ,CAAC,GAAG,WAAW,GAClEW,KAAK,CAACpC,KAAK,CAACwC,IAAI,CAAChB,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,OAAO,CAAC,GAAG,OAAO,GAC3DW,KAAK,CAACpC,KAAK,CAACwC,IAAI,CAAChB,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,gBAAgB,CAAC,GAAG,WAAW,GAAG,SAAS;MACpG,CAAC,CAAC;MAEF/B,SAAS,CAACuB,QAAQ,CAAC;MACnBf,YAAY,CAACU,OAAO,CAACQ,IAAI,IAAI,EAAE,CAAC;MAChCxB,kBAAkB,CAACiB,YAAY,CAACO,IAAI,IAAI,EAAE,CAAC;IAC7C,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACxC,YAAY,IAAI,CAACN,QAAQ,CAACE,QAAQ,EAAE;IAEzC,MAAM6C,QAAQ,GAAG3C,SAAS,CAAC4C,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAACsB,EAAE,KAAK3C,YAAY,CAAC;IACjE,IAAIyC,QAAQ,EAAE;MACZ9C,WAAW,CAACiD,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP/C,KAAK,EAAE,CAAC,GAAG+C,IAAI,CAAC/C,KAAK,EAAE;UACrB8C,EAAE,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,YAAY,EAAEN,QAAQ,CAACE,EAAE;UACzBK,SAAS,EAAEP,QAAQ,CAACQ,IAAI;UACxBC,kBAAkB,EAAET,QAAQ,CAACS,kBAAkB;UAC/CnB,mBAAmB,EAAEU,QAAQ,CAACU,kBAAkB;UAChDC,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC,CAAC,CAAC;MACHnD,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC;EAED,MAAMoD,mBAAmB,GAAIC,MAAM,IAAK;IACtC3D,WAAW,CAACiD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP/C,KAAK,EAAE+C,IAAI,CAAC/C,KAAK,CAAC0D,MAAM,CAAClC,IAAI,IAAIA,IAAI,CAACsB,EAAE,KAAKW,MAAM;IACrD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC9D,QAAQ,CAACE,QAAQ,IAAIF,QAAQ,CAACG,KAAK,CAAC4D,MAAM,KAAK,CAAC,EAAE;IAEvD,IAAI;MACF,KAAK,MAAMpC,IAAI,IAAI3B,QAAQ,CAACG,KAAK,EAAE;QACjC,MAAMd,KAAK,CAAC2E,IAAI,CAAC,GAAGxE,QAAQ,qBAAqB,EAAE;UACjDU,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;UAC3BoD,SAAS,EAAE3B,IAAI,CAAC2B,SAAS;UACzBE,kBAAkB,EAAE7B,IAAI,CAAC6B,kBAAkB;UAC3CnB,mBAAmB,EAAEV,IAAI,CAACU,mBAAmB;UAC7CqB,QAAQ,EAAE/B,IAAI,CAAC+B;QACjB,CAAC,CAAC;MACJ;MAEAzD,WAAW,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAG,CAAC,CAAC;MACxCO,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMqB,mBAAmB,GAAIrC,MAAM,IAAK;IACtC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACErC,OAAA;IAAK2E,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B5E,OAAA;MAAK2E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B5E,OAAA;QAAA4E,QAAA,EAAI;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtChF,OAAA;QACEiF,OAAO,EAAE9E,iBAAkB;QAC3BwE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5E,OAAA;QAAA4E,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBhF,OAAA;QAAK2E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB5E,OAAA;UACEkF,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,0BAA0B;UACtCC,KAAK,EAAE3E,QAAQ,CAACE,QAAS;UACzB0E,QAAQ,EAAGC,CAAC,IAAK5E,WAAW,CAACiD,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEhD,QAAQ,EAAE2E,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eAEFhF,OAAA;UAAK2E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B5E,OAAA;YACEoF,KAAK,EAAErE,YAAa;YACpBsE,QAAQ,EAAGC,CAAC,IAAKtE,eAAe,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAR,QAAA,gBAEjD5E,OAAA;cAAQoF,KAAK,EAAC,EAAE;cAAAR,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACzCnE,SAAS,CAAC2E,GAAG,CAACpD,IAAI,iBACjBpC,OAAA;cAAsBoF,KAAK,EAAEhD,IAAI,CAACsB,EAAG;cAAAkB,QAAA,GAClCxC,IAAI,CAAC4B,IAAI,EAAC,IAAE,EAAC5B,IAAI,CAAC8B,kBAAkB,EAAC,MACxC;YAAA,GAFa9B,IAAI,CAACsB,EAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACThF,OAAA;YAAQiF,OAAO,EAAE1B,cAAe;YAACkC,QAAQ,EAAE,CAAC1E,YAAa;YAAA6D,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELvE,QAAQ,CAACG,KAAK,CAAC4D,MAAM,GAAG,CAAC,iBACxBxE,OAAA;UAAK2E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5E,OAAA;YAAA4E,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACpBvE,QAAQ,CAACG,KAAK,CAAC4E,GAAG,CAACpD,IAAI,iBACtBpC,OAAA;YAAmB2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvC5E,OAAA;cAAA4E,QAAA,GAAOxC,IAAI,CAAC2B,SAAS,EAAC,IAAE,EAAC3B,IAAI,CAACU,mBAAmB,EAAC,MAAI;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7DhF,OAAA;cACEoF,KAAK,EAAEhD,IAAI,CAAC+B,QAAS;cACrBkB,QAAQ,EAAGC,CAAC,IAAK;gBACf5E,WAAW,CAACiD,IAAI,KAAK;kBACnB,GAAGA,IAAI;kBACP/C,KAAK,EAAE+C,IAAI,CAAC/C,KAAK,CAAC4E,GAAG,CAACE,CAAC,IACrBA,CAAC,CAAChC,EAAE,KAAKtB,IAAI,CAACsB,EAAE,GAAG;oBAAE,GAAGgC,CAAC;oBAAEvB,QAAQ,EAAEmB,CAAC,CAACC,MAAM,CAACH;kBAAM,CAAC,GAAGM,CAC1D;gBACF,CAAC,CAAC,CAAC;cACL,CAAE;cAAAd,QAAA,gBAEF5E,OAAA;gBAAQoF,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChChF,OAAA;gBAAQoF,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtChF,OAAA;gBAAQoF,KAAK,EAAC,MAAM;gBAAAR,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClChF,OAAA;gBAAQoF,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACThF,OAAA;cAAQiF,OAAO,EAAEA,CAAA,KAAMb,mBAAmB,CAAChC,IAAI,CAACsB,EAAE,CAAE;cAAAkB,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GAlBvD5C,IAAI,CAACsB,EAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBZ,CACN,CAAC,eACFhF,OAAA;YAAQiF,OAAO,EAAEV,WAAY;YAACI,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5E,OAAA;QAAK2E,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B5E,OAAA;UAAK2E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5E,OAAA;YACE2E,SAAS,EAAE,WAAW1D,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC/DgE,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,QAAQ,CAAE;YAAA0D,QAAA,GACvC,8BACmB,EAAC9C,MAAM,CAAC6D,IAAI,CAACtF,MAAM,CAAC,CAACmE,MAAM,EAAC,GAChD;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThF,OAAA;YACE2E,SAAS,EAAE,WAAW1D,SAAS,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;YAClEgE,OAAO,EAAEA,CAAA,KAAM/D,YAAY,CAAC,WAAW,CAAE;YAAA0D,QAAA,GAC1C,2BACqB,EAACrE,eAAe,CAACiE,MAAM,EAAC,GAC9C;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhF,OAAA;QAAK2E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAC9B3D,SAAS,KAAK,QAAQ,GACrBa,MAAM,CAACC,MAAM,CAAC1B,MAAM,CAAC,CAACmF,GAAG,CAACxC,KAAK,iBAC7BhD,OAAA;UAA0B2E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC9C5E,OAAA;YAAK2E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5E,OAAA;cAAA4E,QAAA,EAAK5B,KAAK,CAACrC;YAAQ;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzBhF,OAAA;cACE2E,SAAS,EAAC,cAAc;cACxBiB,KAAK,EAAE;gBAAEC,eAAe,EAAEnB,mBAAmB,CAAC1B,KAAK,CAACX,MAAM;cAAE,CAAE;cAAAuC,QAAA,EAE7D5B,KAAK,CAACX,MAAM,CAACyD,WAAW,CAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAK2E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5E,OAAA;gBAAA4E,QAAA,GAAM,eAAG,EAAC5B,KAAK,CAACpC,KAAK,CAAC4D,MAAM,EAAC,QAAM;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1ChF,OAAA;gBAAA4E,QAAA,GAAM,eAAG,EAAC5B,KAAK,CAACV,eAAe,EAAC,KAAG;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1ChF,OAAA;gBAAA4E,QAAA,GAAM,eAAG,EAAC5B,KAAK,CAACT,QAAQ,CAACiC,MAAM,EAAC,WAAS;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3B5B,KAAK,CAACT,QAAQ,CAACiD,GAAG,CAACO,OAAO,iBACzB/F,OAAA;gBAAoB2E,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEmB;cAAO,GAAzCA,OAAO;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAyC,CAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B5B,KAAK,CAACpC,KAAK,CAAC4E,GAAG,CAACpD,IAAI,iBACnBpC,OAAA;cAAmB2E,SAAS,EAAE,YAAYvC,IAAI,CAACC,MAAM,EAAG;cAAAuC,QAAA,gBACtD5E,OAAA;gBAAK2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5E,OAAA;kBAAM2E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAExC,IAAI,CAAC2B;gBAAS;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDhF,OAAA;kBAAM2E,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAExC,IAAI,CAACM;gBAAY;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNhF,OAAA;gBAAK2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B5E,OAAA;kBAAM2E,SAAS,EAAC,UAAU;kBAAAC,QAAA,GAAC,GAAC,EAACxC,IAAI,CAAC4D,eAAe;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDhF,OAAA;kBAAM2E,SAAS,EAAE,UAAUvC,IAAI,CAACC,MAAM,EAAG;kBAAAuC,QAAA,EACtCxC,IAAI,CAACC,MAAM,CAAC4D,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACH,WAAW,CAAC;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAVE5C,IAAI,CAACsB,EAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAELhC,KAAK,CAACX,MAAM,KAAK,OAAO,iBACvBrC,OAAA;YAAK2E,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5E,OAAA;cAAQ2E,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA,GA/COhC,KAAK,CAACrC,QAAQ;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgDnB,CACN,CAAC,GAEFzE,eAAe,CAACiF,GAAG,CAACxC,KAAK,iBACvBhD,OAAA;UAAoB2E,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAClD5E,OAAA;YAAK2E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5E,OAAA;cAAA4E,QAAA,EAAK5B,KAAK,CAACrC;YAAQ;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzBhF,OAAA;cAAK2E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC5E,OAAA;cAAK2E,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5E,OAAA;gBAAM2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDhF,OAAA;gBAAM2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE5B,KAAK,CAACkD,sBAAsB,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5E,OAAA;gBAAM2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/ChF,OAAA;gBAAM2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE5B,KAAK,CAACoD,eAAe,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5E,OAAA;gBAAM2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDhF,OAAA;gBAAM2E,SAAS,EAAE,gBAAgB3B,KAAK,CAACqD,gBAAgB,IAAI,CAAC,GAAG,MAAM,GAAG,mBAAmB,EAAG;gBAAAzB,QAAA,GAC3F,CAAC5B,KAAK,CAACqD,gBAAgB,GAAG,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5E,OAAA;cAAK2E,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5E,OAAA;gBAAA4E,QAAA,GAAM,eAAG,EAAC5B,KAAK,CAACsD,WAAW,EAAC,QAAM;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzChF,OAAA;gBAAA4E,QAAA,GAAM,eAAG,EAAC5B,KAAK,CAACuD,qBAAqB,CAAC/B,MAAM,EAAC,WAAS;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7DhF,OAAA;gBAAM2E,SAAS,EAAE3B,KAAK,CAACwD,mBAAmB,GAAG,CAAC,GAAG,WAAW,GAAG,YAAa;gBAAA5B,QAAA,GACzE5B,KAAK,CAACwD,mBAAmB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAExD,KAAK,CAACwD,mBAAmB,CAACL,OAAO,CAAC,CAAC,CAAC,EAAC,iBAClF;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,aACpB,EAAC,IAAIhB,IAAI,CAACZ,KAAK,CAACyD,kBAAkB,CAAC,CAACC,cAAc,CAAC,CAAC;YAAA;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAlCEhC,KAAK,CAACU,EAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmCb,CACN;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5E,EAAA,CAvTQF,aAAa;AAAAyG,EAAA,GAAbzG,aAAa;AAyTtB,eAAeA,aAAa;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}