from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn

from .core.database import create_tables
from .core.redis_client import close_redis
from .api.endpoints import router
from .api.menu_endpoints import router as menu_router
from .api.queue_endpoints import router as queue_router
from .api.simple_sequencing import router as sequencing_router
from .api.ai_agent_sequencing import router as ai_agent_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    create_tables()
    print("🚀 KDS AI Sequencing Service Started")
    print("📊 Database tables created")
    print("🤖 AI Engine initialized")
    
    yield
    
    # Shutdown
    await close_redis()
    print("🛑 KDS AI Sequencing Service Stopped")

app = FastAPI(
    title="KDS AI Sequencing Microservice",
    description="Intelligent Kitchen Display System with AI-powered order sequencing",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api/v1", tags=["KDS Basic"])
app.include_router(menu_router, prefix="/api/v1", tags=["Menu Management"])
app.include_router(queue_router, prefix="/api/v1", tags=["Queue Management"])
app.include_router(sequencing_router, prefix="/api/v1", tags=["Smart Sequencing"])
app.include_router(ai_agent_router, prefix="/api/v1", tags=["AI Agent Sequencing"])

@app.get("/")
async def root():
    return {
        "service": "KDS AI Sequencing Microservice",
        "version": "1.0.0",
        "status": "running",
        "features": [
            "AI-powered order sequencing",
            "Real-time queue management",
            "Automatic status transitions",
            "Performance analytics",
            "Multi-kitchen support",
            "Historical learning"
        ]
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "kds-ai-sequencing",
        "timestamp": "2024-01-01T00:00:00Z"
    }

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )