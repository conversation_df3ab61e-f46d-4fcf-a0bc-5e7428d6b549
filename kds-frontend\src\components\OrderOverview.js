import React, { useState, useEffect } from 'react';
import axios from 'axios';

const API_BASE = 'http://localhost:8000/api/v1';

function OrderOverview({ onSwitchToKitchen }) {
  const [orders, setOrders] = useState({});
  const [completedOrders, setCompletedOrders] = useState([]);
  const [newOrder, setNewOrder] = useState({
    order_id: '',
    items: []
  });

  const generateOrderNumber = () => {
    return Math.floor(1000000000 + Math.random() * 9000000000).toString();
  };
  const [menuItems, setMenuItems] = useState([]);
  const [selectedItem, setSelectedItem] = useState('');
  const [activeTab, setActiveTab] = useState('active');

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 3000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    try {
      const [queuesRes, menuRes, completedRes] = await Promise.all([
        axios.get(`${API_BASE}/queue/all`),
        axios.get(`${API_BASE}/menu-items`),
        axios.get(`${API_BASE}/orders/completed?limit=20&hours=24`)
      ]);
      
      // Group items by order_id
      const orderMap = {};
      Object.values(queuesRes.data.queues || {}).forEach(queue => {
        queue.items.forEach(item => {
          if (!orderMap[item.order_id]) {
            orderMap[item.order_id] = {
              order_id: item.order_id,
              items: [],
              status: 'pending',
              total_prep_time: 0,
              stations: new Set()
            };
          }
          orderMap[item.order_id].items.push({
            ...item,
            kitchen_name: queue.station_name
          });
          orderMap[item.order_id].total_prep_time = Math.max(
            orderMap[item.order_id].total_prep_time,
            item.estimated_prep_time
          );
          orderMap[item.order_id].stations.add(queue.station_name);
        });
      });

      // Convert stations Set to Array
      Object.values(orderMap).forEach(order => {
        order.stations = Array.from(order.stations);
        order.status = order.items.every(item => item.status === 'served') ? 'completed' :
                      order.items.some(item => item.status === 'ready') ? 'ready' :
                      order.items.some(item => item.status === 'in_preparation') ? 'preparing' : 'pending';
      });

      setOrders(orderMap);
      setMenuItems(menuRes.data || []);
      setCompletedOrders(completedRes.data || []);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const addItemToOrder = () => {
    if (!selectedItem || !newOrder.order_id) return;
    
    const menuItem = menuItems.find(item => item.id === selectedItem);
    if (menuItem) {
      setNewOrder(prev => ({
        ...prev,
        items: [...prev.items, {
          id: Date.now(),
          menu_item_id: menuItem.id,
          item_name: menuItem.name,
          kitchen_station_id: menuItem.kitchen_station_id,
          estimated_prep_time: menuItem.standard_prep_time,
          priority: 'medium'
        }]
      }));
      setSelectedItem('');
    }
  };

  const removeItemFromOrder = (itemId) => {
    setNewOrder(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  const submitOrder = async () => {
    if (!newOrder.order_id || newOrder.items.length === 0) return;

    try {
      for (const item of newOrder.items) {
        await axios.post(`${API_BASE}/items/ai-sequenced`, {
          order_id: newOrder.order_id,
          item_name: item.item_name,
          kitchen_station_id: item.kitchen_station_id,
          estimated_prep_time: item.estimated_prep_time,
          priority: item.priority
        });
      }
      
      setNewOrder({ order_id: '', items: [] });
      fetchData();
    } catch (error) {
      console.error('Error creating order:', error);
    }
  };

  const getOrderStatusColor = (status) => {
    switch (status) {
      case 'completed': return '#4caf50';
      case 'ready': return '#ff9800';
      case 'preparing': return '#2196f3';
      default: return '#9e9e9e';
    }
  };

  return (
    <div className="order-overview">
      <div className="overview-header">
        <h1>📋 Order Management Dashboard</h1>
        <button 
          onClick={onSwitchToKitchen}
          className="switch-mode-btn"
        >
          🍳 Switch to Kitchen Manager
        </button>
      </div>

      {/* New Order Creation */}
      <div className="new-order-section">
        <h2>Create New Order</h2>
        <div className="order-form">
          <div className="order-id-section">
            <input
              type="text"
              placeholder="Order Number"
              value={newOrder.order_id}
              onChange={(e) => setNewOrder(prev => ({ ...prev, order_id: e.target.value }))}
            />
            <button 
              type="button"
              onClick={() => setNewOrder(prev => ({ ...prev, order_id: generateOrderNumber() }))}
              className="generate-order-btn"
            >
              🎲 Generate
            </button>
          </div>
          
          <div className="item-selector">
            <select
              value={selectedItem}
              onChange={(e) => setSelectedItem(e.target.value)}
            >
              <option value="">Select Menu Item</option>
              {menuItems.map(item => (
                <option key={item.id} value={item.id}>
                  {item.name} ({item.standard_prep_time}min)
                </option>
              ))}
            </select>
            <button onClick={addItemToOrder} disabled={!selectedItem}>
              Add Item
            </button>
          </div>

          {newOrder.items.length > 0 && (
            <div className="order-items">
              <h3>Order Items:</h3>
              {newOrder.items.map(item => (
                <div key={item.id} className="order-item">
                  <span>{item.item_name} ({item.estimated_prep_time}min)</span>
                  <select
                    value={item.priority}
                    onChange={(e) => {
                      setNewOrder(prev => ({
                        ...prev,
                        items: prev.items.map(i => 
                          i.id === item.id ? { ...i, priority: e.target.value } : i
                        )
                      }));
                    }}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                  </select>
                  <button onClick={() => removeItemFromOrder(item.id)}>❌</button>
                </div>
              ))}
              <button onClick={submitOrder} className="submit-order-btn">
                🤖 Create Order with AI Sequencing
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Orders Overview */}
      <div className="orders-grid">
        <div className="orders-header">
          <div className="tab-buttons">
            <button 
              className={`tab-btn ${activeTab === 'active' ? 'active' : ''}`}
              onClick={() => setActiveTab('active')}
            >
              📋 Active Orders ({Object.keys(orders).length})
            </button>
            <button 
              className={`tab-btn ${activeTab === 'completed' ? 'active' : ''}`}
              onClick={() => setActiveTab('completed')}
            >
              ✅ Completed Orders ({completedOrders.length})
            </button>
          </div>
        </div>
        <div className="orders-container">
          {activeTab === 'active' ? (
            Object.values(orders).map(order => (
              <div key={order.order_id} className="order-card">
                <div className="order-header">
                  <h3>{order.order_id}</h3>
                  <div 
                    className="order-status"
                    style={{ backgroundColor: getOrderStatusColor(order.status) }}
                  >
                    {order.status.toUpperCase()}
                  </div>
                </div>

                <div className="order-info">
                  <div className="order-stats">
                    <span>📦 {order.items.length} items</span>
                    <span>🕒 {order.total_prep_time}min</span>
                    <span>🏪 {order.stations.length} stations</span>
                  </div>
                  <div className="stations-list">
                    {order.stations.map(station => (
                      <span key={station} className="station-tag">{station}</span>
                    ))}
                  </div>
                </div>

                <div className="order-items-list">
                  {order.items.map(item => (
                    <div key={item.id} className={`item-row ${item.status}`}>
                      <div className="item-info">
                        <span className="item-name">{item.item_name}</span>
                        <span className="item-kitchen">{item.kitchen_name}</span>
                      </div>
                      <div className="item-details">
                        <span className="sequence">#{item.sequence_number}</span>
                        <span className={`status ${item.status}`}>
                          {item.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>

                {order.status === 'ready' && (
                  <div className="order-actions">
                    <button className="complete-order-btn">
                      ✅ Complete Order
                    </button>
                  </div>
                )}
              </div>
            ))
          ) : (
            completedOrders.map(order => (
              <div key={order.id} className="order-card completed">
                <div className="order-header">
                  <h3>{order.order_id}</h3>
                  <div className="completion-badge">✅ COMPLETED</div>
                </div>

                <div className="completion-metrics">
                  <div className="metric">
                    <span className="metric-label">Total Time</span>
                    <span className="metric-value">{order.total_preparation_time.toFixed(1)}min</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Wait Time</span>
                    <span className="metric-value">{order.total_wait_time.toFixed(1)}min</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Efficiency</span>
                    <span className={`metric-value ${order.efficiency_score >= 1 ? 'good' : 'needs-improvement'}`}>
                      {(order.efficiency_score * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>

                <div className="order-summary">
                  <div className="summary-stats">
                    <span>📦 {order.total_items} items</span>
                    <span>🏪 {order.kitchen_stations_used.length} stations</span>
                    <span className={order.actual_vs_estimated > 0 ? 'over-time' : 'under-time'}>
                      {order.actual_vs_estimated > 0 ? '+' : ''}{order.actual_vs_estimated.toFixed(1)}min vs estimate
                    </span>
                  </div>
                  <div className="completion-time">
                    Completed: {new Date(order.order_completed_at).toLocaleString()}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default OrderOverview;