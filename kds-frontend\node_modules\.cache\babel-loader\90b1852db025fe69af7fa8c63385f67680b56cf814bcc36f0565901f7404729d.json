{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar isObject = require('../internals/is-object');\nvar document = globalThis.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};", "map": {"version": 3, "names": ["globalThis", "require", "isObject", "document", "EXISTS", "createElement", "module", "exports", "it"], "sources": ["D:/kds_ai_sequencing/kds-frontend/node_modules/core-js-pure/internals/document-create-element.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar isObject = require('../internals/is-object');\n\nvar document = globalThis.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAEhD,IAAIE,QAAQ,GAAGH,UAAU,CAACG,QAAQ;AAClC;AACA,IAAIC,MAAM,GAAGF,QAAQ,CAACC,QAAQ,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAACE,aAAa,CAAC;AAEnEC,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B,OAAOJ,MAAM,GAAGD,QAAQ,CAACE,aAAa,CAACG,EAAE,CAAC,GAAG,CAAC,CAAC;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}